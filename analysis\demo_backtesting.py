#!/usr/bin/env python3
"""
Demo de Backtesting QUALIA - Versão Simplificada
YAA IMPLEMENTATION: Demonstração funcional com estratégias mock
"""

import pandas as pd
import numpy as np
import json
import warnings
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import time

warnings.filterwarnings('ignore')

# Importa métricas avançadas
try:
    from advanced_metrics import AdvancedPerformanceMetrics
except ImportError:
    print("⚠️  Usando métricas básicas...")

@dataclass
class BacktestConfig:
    """Configuração para backtesting."""
    initial_capital: float = 100000.0
    risk_per_trade_pct: float = 2.0
    commission_rate: float = 0.001
    slippage_rate: float = 0.0005
    min_lookback_periods: int = 100

@dataclass 
class PerformanceMetrics:
    """Métricas de performance padronizadas."""
    strategy_name: str
    symbol: str
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    cagr: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_duration: float
    quantum_advantage: float = 0.0
    cross_modal_coherence: float = 0.0
    final_capital: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class MockStrategy:
    """Estratégia mock base."""
    
    def __init__(self, name: str, symbol: str):
        self.name = name
        self.symbol = symbol
        self.position = 0.0
        
    def analyze_market(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Análise básica - deve ser sobrescrita."""
        return {
            'signal': 0.0,
            'confidence': 0.5,
            'position_size': 0.1
        }

class BuyAndHoldStrategy(MockStrategy):
    """Estratégia Buy and Hold."""
    
    def __init__(self, symbol: str):
        super().__init__("Buy_and_Hold", symbol)
        self.bought = False
        
    def analyze_market(self, data: pd.DataFrame) -> Dict[str, Any]:
        if not self.bought and len(data) > 100:
            self.bought = True
            return {'signal': 1.0, 'confidence': 1.0, 'position_size': 1.0}
        return {'signal': 0.0, 'confidence': 0.5, 'position_size': 0.0}

class MomentumStrategy(MockStrategy):
    """Estratégia de Momentum simples."""
    
    def __init__(self, symbol: str):
        super().__init__("Momentum", symbol)
        
    def analyze_market(self, data: pd.DataFrame) -> Dict[str, Any]:
        if len(data) < 50:
            return {'signal': 0.0, 'confidence': 0.5, 'position_size': 0.0}
            
        # Momentum baseado em SMA
        short_ma = data['close'].rolling(10).mean().iloc[-1]
        long_ma = data['close'].rolling(50).mean().iloc[-1]
        
        if short_ma > long_ma:
            signal = 0.8
        elif short_ma < long_ma:
            signal = -0.8
        else:
            signal = 0.0
            
        return {
            'signal': signal,
            'confidence': 0.7,
            'position_size': 0.5
        }

class MeanReversionStrategy(MockStrategy):
    """Estratégia de Mean Reversion."""
    
    def __init__(self, symbol: str):
        super().__init__("Mean_Reversion", symbol)
        
    def analyze_market(self, data: pd.DataFrame) -> Dict[str, Any]:
        if len(data) < 20:
            return {'signal': 0.0, 'confidence': 0.5, 'position_size': 0.0}
            
        # Mean reversion usando Bollinger Bands
        sma = data['close'].rolling(20).mean()
        std = data['close'].rolling(20).std()
        
        upper_band = sma + (2 * std)
        lower_band = sma - (2 * std)
        
        current_price = data['close'].iloc[-1]
        current_sma = sma.iloc[-1]
        current_upper = upper_band.iloc[-1]
        current_lower = lower_band.iloc[-1]
        
        if current_price > current_upper:
            signal = -0.6  # Sell signal
        elif current_price < current_lower:
            signal = 0.6   # Buy signal
        else:
            signal = 0.0
            
        return {
            'signal': signal,
            'confidence': 0.6,
            'position_size': 0.3
        }

class TrendFollowingStrategy(MockStrategy):
    """Estratégia de Trend Following."""
    
    def __init__(self, symbol: str):
        super().__init__("Trend_Following", symbol)
        
    def analyze_market(self, data: pd.DataFrame) -> Dict[str, Any]:
        if len(data) < 30:
            return {'signal': 0.0, 'confidence': 0.5, 'position_size': 0.0}
            
        # Trend usando múltiplas médias móveis
        ema_fast = data['close'].ewm(span=12).mean().iloc[-1]
        ema_slow = data['close'].ewm(span=26).mean().iloc[-1]
        
        # MACD simples
        macd = ema_fast - ema_slow
        signal_line = data['close'].ewm(span=9).mean().iloc[-1]
        
        if macd > 0 and ema_fast > ema_slow:
            signal = 0.7
        elif macd < 0 and ema_fast < ema_slow:
            signal = -0.7
        else:
            signal = 0.0
            
        return {
            'signal': signal,
            'confidence': 0.8,
            'position_size': 0.6
        }

class VolatilityBreakoutStrategy(MockStrategy):
    """Estratégia de Volatility Breakout."""
    
    def __init__(self, symbol: str):
        super().__init__("Volatility_Breakout", symbol)
        
    def analyze_market(self, data: pd.DataFrame) -> Dict[str, Any]:
        if len(data) < 20:
            return {'signal': 0.0, 'confidence': 0.5, 'position_size': 0.0}
            
        # Volatility breakout usando ATR
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        atr = true_range.rolling(14).mean()
        
        current_atr = atr.iloc[-1]
        avg_atr = atr.rolling(50).mean().iloc[-1]
        
        # Breakout quando volatilidade aumenta
        if current_atr > avg_atr * 1.5:
            # Determina direção baseada no preço
            recent_return = (data['close'].iloc[-1] / data['close'].iloc[-5]) - 1
            signal = 0.5 if recent_return > 0 else -0.5
        else:
            signal = 0.0
            
        return {
            'signal': signal,
            'confidence': 0.6,
            'position_size': 0.4
        }

class RSIStrategy(MockStrategy):
    """Estratégia baseada em RSI."""
    
    def __init__(self, symbol: str):
        super().__init__("RSI_Strategy", symbol)
        
    def analyze_market(self, data: pd.DataFrame) -> Dict[str, Any]:
        if len(data) < 15:
            return {'signal': 0.0, 'confidence': 0.5, 'position_size': 0.0}
            
        # Calcula RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        current_rsi = rsi.iloc[-1]
        
        if current_rsi < 30:  # Oversold
            signal = 0.6
        elif current_rsi > 70:  # Overbought
            signal = -0.6
        else:
            signal = 0.0
            
        return {
            'signal': signal,
            'confidence': 0.7,
            'position_size': 0.4
        }

class DemoBacktestingFramework:
    """Framework de demonstração para backtesting."""
    
    def __init__(self, config: BacktestConfig = None):
        self.config = config or BacktestConfig()
        self.results = {}
        
        # Estratégias disponíveis
        self.strategy_classes = {
            'BuyAndHold': BuyAndHoldStrategy,
            'Momentum': MomentumStrategy,
            'MeanReversion': MeanReversionStrategy,
            'TrendFollowing': TrendFollowingStrategy,
            'VolatilityBreakout': VolatilityBreakoutStrategy,
            'RSI': RSIStrategy
        }
        
    def load_market_data(self, file_path: str, symbol: str) -> pd.DataFrame:
        """Carrega dados de mercado."""
        print(f"📊 Carregando dados para {symbol}...")
        
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('datetime', inplace=True)
        
        # Adiciona colunas derivadas
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        df['volatility'] = df['returns'].rolling(24).std()
        
        print(f"✅ Dados carregados: {len(df)} registros")
        return df
