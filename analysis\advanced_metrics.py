#!/usr/bin/env python3
"""
Métricas Avançadas de Performance QUALIA
YAA IMPLEMENTATION: Implementação completa de todas as métricas solicitadas
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class AdvancedPerformanceMetrics:
    """Calculadora de métricas avançadas de performance."""
    
    @staticmethod
    def calculate_sharpe_ratio(returns: np.ndarray, risk_free_rate: float = 0.02) -> float:
        """Calcula Sharpe Ratio anualizado."""
        if len(returns) == 0:
            return 0.0
            
        excess_returns = returns - risk_free_rate / (365 * 24)  # Ajuste para dados horários
        
        if np.std(excess_returns) == 0:
            return 0.0
            
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(365 * 24)
    
    @staticmethod
    def calculate_sortino_ratio(returns: np.ndarray, risk_free_rate: float = 0.02, 
                              target_return: float = 0.0) -> float:
        """Calcula Sortino Ratio usando apenas downside deviation."""
        if len(returns) == 0:
            return 0.0
            
        excess_returns = returns - risk_free_rate / (365 * 24)
        downside_returns = excess_returns[excess_returns < target_return]
        
        if len(downside_returns) == 0:
            return float('inf') if np.mean(excess_returns) > 0 else 0.0
            
        downside_deviation = np.std(downside_returns)
        if downside_deviation == 0:
            return 0.0
            
        return np.mean(excess_returns) / downside_deviation * np.sqrt(365 * 24)
    
    @staticmethod
    def calculate_max_drawdown(equity_curve: np.ndarray) -> Tuple[float, int, int]:
        """Calcula Maximum Drawdown e períodos de início/fim."""
        if len(equity_curve) == 0:
            return 0.0, 0, 0
            
        # Calcula running maximum
        running_max = np.maximum.accumulate(equity_curve)
        
        # Calcula drawdown
        drawdown = (equity_curve - running_max) / running_max
        
        # Encontra maximum drawdown
        max_dd = np.min(drawdown)
        max_dd_idx = np.argmin(drawdown)
        
        # Encontra início do drawdown (último peak antes do max DD)
        peak_idx = 0
        for i in range(max_dd_idx, -1, -1):
            if equity_curve[i] == running_max[i]:
                peak_idx = i
                break
                
        return abs(max_dd), peak_idx, max_dd_idx
    
    @staticmethod
    def calculate_cagr(initial_value: float, final_value: float, periods: int, 
                      periods_per_year: int = 365 * 24) -> float:
        """Calcula Compound Annual Growth Rate."""
        if initial_value <= 0 or periods <= 0:
            return 0.0
            
        years = periods / periods_per_year
        if years <= 0:
            return 0.0
            
        return (final_value / initial_value) ** (1 / years) - 1
    
    @staticmethod
    def calculate_calmar_ratio(returns: np.ndarray, equity_curve: np.ndarray) -> float:
        """Calcula Calmar Ratio (CAGR / Max Drawdown)."""
        if len(returns) == 0 or len(equity_curve) == 0:
            return 0.0
            
        # CAGR
        periods = len(returns)
        cagr = AdvancedPerformanceMetrics.calculate_cagr(
            equity_curve[0], equity_curve[-1], periods
        )
        
        # Max Drawdown
        max_dd, _, _ = AdvancedPerformanceMetrics.calculate_max_drawdown(equity_curve)
        
        if max_dd == 0:
            return float('inf') if cagr > 0 else 0.0
            
        return cagr / max_dd
    
    @staticmethod
    def calculate_information_ratio(returns: np.ndarray, benchmark_returns: np.ndarray) -> float:
        """Calcula Information Ratio vs benchmark."""
        if len(returns) == 0 or len(benchmark_returns) == 0:
            return 0.0
            
        # Alinha os arrays
        min_len = min(len(returns), len(benchmark_returns))
        returns = returns[:min_len]
        benchmark_returns = benchmark_returns[:min_len]
        
        excess_returns = returns - benchmark_returns
        tracking_error = np.std(excess_returns)
        
        if tracking_error == 0:
            return 0.0
            
        return np.mean(excess_returns) / tracking_error * np.sqrt(365 * 24)
    
    @staticmethod
    def calculate_omega_ratio(returns: np.ndarray, threshold: float = 0.0) -> float:
        """Calcula Omega Ratio."""
        if len(returns) == 0:
            return 0.0
            
        gains = returns[returns > threshold] - threshold
        losses = threshold - returns[returns < threshold]
        
        total_gains = np.sum(gains) if len(gains) > 0 else 0
        total_losses = np.sum(losses) if len(losses) > 0 else 0
        
        if total_losses == 0:
            return float('inf') if total_gains > 0 else 1.0
            
        return total_gains / total_losses
    
    @staticmethod
    def calculate_var_cvar(returns: np.ndarray, confidence_level: float = 0.05) -> Tuple[float, float]:
        """Calcula Value at Risk e Conditional Value at Risk."""
        if len(returns) == 0:
            return 0.0, 0.0
            
        # VaR
        var = np.percentile(returns, confidence_level * 100)
        
        # CVaR (Expected Shortfall)
        cvar_returns = returns[returns <= var]
        cvar = np.mean(cvar_returns) if len(cvar_returns) > 0 else var
        
        return abs(var), abs(cvar)
    
    @staticmethod
    def calculate_skewness_kurtosis(returns: np.ndarray) -> Tuple[float, float]:
        """Calcula Skewness e Kurtosis dos retornos."""
        if len(returns) < 3:
            return 0.0, 0.0
            
        skewness = stats.skew(returns)
        kurtosis = stats.kurtosis(returns)
        
        return skewness, kurtosis
    
    @staticmethod
    def calculate_win_loss_metrics(trades: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calcula métricas de win/loss dos trades."""
        if not trades:
            return {
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'largest_win': 0.0,
                'largest_loss': 0.0,
                'expectancy': 0.0
            }
        
        # Calcula P&L de cada trade (simplificado)
        pnls = []
        for i in range(1, len(trades)):
            if 'size' in trades[i] and 'price' in trades[i]:
                # Aproximação do P&L baseada no tamanho e preço
                pnl = trades[i]['size'] * (trades[i]['price'] - trades[i-1].get('price', trades[i]['price']))
                pnls.append(pnl)
        
        if not pnls:
            return {
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'largest_win': 0.0,
                'largest_loss': 0.0,
                'expectancy': 0.0
            }
        
        wins = [p for p in pnls if p > 0]
        losses = [p for p in pnls if p < 0]
        
        win_rate = len(wins) / len(pnls) if pnls else 0.0
        
        total_wins = sum(wins) if wins else 0.0
        total_losses = abs(sum(losses)) if losses else 0.0
        profit_factor = total_wins / total_losses if total_losses > 0 else 0.0
        
        avg_win = np.mean(wins) if wins else 0.0
        avg_loss = abs(np.mean(losses)) if losses else 0.0
        
        largest_win = max(wins) if wins else 0.0
        largest_loss = abs(min(losses)) if losses else 0.0
        
        expectancy = np.mean(pnls) if pnls else 0.0
        
        return {
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss,
            'expectancy': expectancy
        }

    @staticmethod
    def calculate_quantum_advantage(strategy_returns: np.ndarray,
                                  benchmark_returns: np.ndarray,
                                  quantum_signals: Optional[np.ndarray] = None) -> float:
        """
        Calcula Quantum Advantage - métrica específica QUALIA.
        Mede o benefício adicional das características quânticas da estratégia.
        """
        if len(strategy_returns) == 0 or len(benchmark_returns) == 0:
            return 0.0

        # Alinha arrays
        min_len = min(len(strategy_returns), len(benchmark_returns))
        strategy_returns = strategy_returns[:min_len]
        benchmark_returns = benchmark_returns[:min_len]

        # Calcula excess returns
        excess_returns = strategy_returns - benchmark_returns

        # Se temos sinais quânticos, usa-os para ponderar o advantage
        if quantum_signals is not None and len(quantum_signals) >= min_len:
            quantum_signals = quantum_signals[:min_len]
            # Normaliza sinais quânticos
            quantum_strength = np.abs(quantum_signals)
            quantum_strength = quantum_strength / (np.max(quantum_strength) + 1e-8)

            # Quantum advantage ponderado pela força dos sinais quânticos
            weighted_excess = excess_returns * quantum_strength
            quantum_advantage = np.mean(weighted_excess) * np.sqrt(365 * 24)
        else:
            # Fallback: usa volatility-adjusted excess return
            volatility_ratio = np.std(strategy_returns) / (np.std(benchmark_returns) + 1e-8)
            quantum_advantage = np.mean(excess_returns) * volatility_ratio * np.sqrt(365 * 24)

        return quantum_advantage

    @staticmethod
    def calculate_cross_modal_coherence(price_data: pd.DataFrame,
                                      volume_data: Optional[np.ndarray] = None,
                                      sentiment_data: Optional[np.ndarray] = None) -> float:
        """
        Calcula Cross-Modal Coherence - métrica específica QUALIA.
        Mede a coerência entre diferentes modalidades de dados (preço, volume, sentimento).
        """
        if len(price_data) == 0:
            return 0.0

        coherence_scores = []

        # Coerência preço-volume
        if volume_data is not None and len(volume_data) >= len(price_data):
            price_returns = price_data['close'].pct_change().dropna()
            volume_changes = pd.Series(volume_data[:len(price_returns)]).pct_change().dropna()

            # Alinha séries
            min_len = min(len(price_returns), len(volume_changes))
            if min_len > 10:
                price_ret_aligned = price_returns.iloc[:min_len]
                volume_ch_aligned = volume_changes.iloc[:min_len]

                # Correlação entre retornos de preço e mudanças de volume
                correlation = np.corrcoef(price_ret_aligned, volume_ch_aligned)[0, 1]
                if not np.isnan(correlation):
                    coherence_scores.append(abs(correlation))

        # Coerência preço-sentimento
        if sentiment_data is not None and len(sentiment_data) >= len(price_data):
            price_returns = price_data['close'].pct_change().dropna()
            sentiment_changes = pd.Series(sentiment_data[:len(price_returns)]).pct_change().dropna()

            min_len = min(len(price_returns), len(sentiment_changes))
            if min_len > 10:
                price_ret_aligned = price_returns.iloc[:min_len]
                sent_ch_aligned = sentiment_changes.iloc[:min_len]

                correlation = np.corrcoef(price_ret_aligned, sent_ch_aligned)[0, 1]
                if not np.isnan(correlation):
                    coherence_scores.append(abs(correlation))

        # Coerência temporal (autocorrelação dos retornos)
        price_returns = price_data['close'].pct_change().dropna()
        if len(price_returns) > 24:  # Pelo menos 24 períodos
            # Autocorrelação com lag de 1 período
            autocorr_1 = np.corrcoef(price_returns[:-1], price_returns[1:])[0, 1]
            if not np.isnan(autocorr_1):
                coherence_scores.append(abs(autocorr_1))

            # Autocorrelação com lag de 24 períodos (1 dia)
            if len(price_returns) > 48:
                autocorr_24 = np.corrcoef(price_returns[:-24], price_returns[24:])[0, 1]
                if not np.isnan(autocorr_24):
                    coherence_scores.append(abs(autocorr_24))

        # Retorna média das coerências calculadas
        if coherence_scores:
            return np.mean(coherence_scores)
        else:
            return 0.0

    @staticmethod
    def calculate_quantum_entropy(returns: np.ndarray, window_size: int = 24) -> float:
        """
        Calcula Quantum Entropy - medida de incerteza quântica nos retornos.
        Baseado na entropia de Shannon dos retornos discretizados.
        """
        if len(returns) < window_size:
            return 0.0

        # Remove NaN e infinitos
        clean_returns = returns[np.isfinite(returns)]
        if len(clean_returns) == 0:
            return 0.0

        # Discretiza retornos em bins
        n_bins = min(10, len(clean_returns) // 5)  # Adaptativo ao tamanho dos dados
        if n_bins < 2:
            return 0.0

        hist, _ = np.histogram(clean_returns, bins=n_bins)

        # Calcula probabilidades
        probabilities = hist / np.sum(hist)
        probabilities = probabilities[probabilities > 0]  # Remove zeros

        # Calcula entropia de Shannon
        entropy = -np.sum(probabilities * np.log2(probabilities))

        # Normaliza pela entropia máxima possível
        max_entropy = np.log2(len(probabilities))
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0.0

        return normalized_entropy

    @staticmethod
    def calculate_tsvf_coherence(forward_signals: np.ndarray,
                               backward_signals: np.ndarray) -> float:
        """
        Calcula TSVF Coherence - métrica específica para estratégias TSVF.
        Mede a coerência entre sinais forward e backward no formalismo TSVF.
        """
        if len(forward_signals) == 0 or len(backward_signals) == 0:
            return 0.0

        # Alinha arrays
        min_len = min(len(forward_signals), len(backward_signals))
        forward_aligned = forward_signals[:min_len]
        backward_aligned = backward_signals[:min_len]

        # Remove NaN e infinitos
        valid_mask = np.isfinite(forward_aligned) & np.isfinite(backward_aligned)
        if np.sum(valid_mask) < 10:
            return 0.0

        forward_clean = forward_aligned[valid_mask]
        backward_clean = backward_aligned[valid_mask]

        # Calcula correlação entre sinais forward e backward
        correlation = np.corrcoef(forward_clean, backward_clean)[0, 1]

        if np.isnan(correlation):
            return 0.0

        # TSVF coherence é baseada na correlação, mas considera também a fase
        # Calcula diferença de fase usando transformada de Hilbert (simplificada)
        try:
            from scipy.signal import hilbert

            # Sinais analíticos
            forward_analytic = hilbert(forward_clean)
            backward_analytic = hilbert(backward_clean)

            # Diferenças de fase
            forward_phase = np.angle(forward_analytic)
            backward_phase = np.angle(backward_analytic)
            phase_diff = np.abs(forward_phase - backward_phase)

            # Coerência de fase (menor diferença = maior coerência)
            phase_coherence = 1.0 - np.mean(phase_diff) / np.pi

            # Combina correlação e coerência de fase
            tsvf_coherence = (abs(correlation) + phase_coherence) / 2.0

        except ImportError:
            # Fallback se scipy não disponível
            tsvf_coherence = abs(correlation)

        return max(0.0, min(1.0, tsvf_coherence))  # Clamp entre 0 e 1
