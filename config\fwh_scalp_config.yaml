# Configuração FWH Scalp Trading - Paper Trading Mode
# Otimizada para scalping de alta frequência com a estratégia Fibonacci Wave Hype

# Configuração da Estratégia FWH
fibonacci_wave_hype_config:
  name: FibonacciWaveHypeStrategy
  enabled: true
  params:
    # Parâmetros otimizados para scalping REALISTA - SELETIVO E LUCRATIVO
    fib_lookback: 20              # Lookback adequado para análise confiável
    hype_threshold: 0.15          # Threshold seletivo para sinais de qualidade
    wave_min_strength: 0.3        # Força mínima para momentum real
    quantum_boost_factor: 1.1     # Boost conservador
    holographic_weight: 0.5       # Peso holográfico balanceado
    tsvf_validation_threshold: 0.4 # Validação rigorosa
    sentiment_cache_ttl: 60       # Cache de 1 minuto
    
    # Configurações específicas para scalping
    scalping_mode: true
    fast_execution: true
    profit_target_pct: 0.5        # 0.5% profit target
    stop_loss_pct: 0.3            # 0.3% stop loss
    max_trade_duration_minutes: 15 # Máximo 15 minutos por trade
    
    # Níveis de Fibonacci ajustados para scalping
    fibonacci_levels:
      primary: [0.236, 0.382, 0.618]
      secondary: [0.146, 0.5, 0.786]
      extensions: [1.272, 1.618]   # Extensões reduzidas
    
    # Detecção de ondas para alta frequência
    wave_detection:
      min_wave_bars: 3             # Ondas menores
      max_wave_bars: 10            # Máximo reduzido
      volume_weight: 0.7           # Maior peso no volume
      price_weight: 0.3
      momentum_threshold: 0.3      # Threshold de momentum
    
    # Integração holográfica otimizada
    holographic_integration:
      cache_enabled: true
      cache_ttl: 60               # Cache de 1 minuto
      boost_range: [0.8, 1.5]    # Range menor para estabilidade
      confidence_threshold: 0.6
      cluster_relevance_map:
        # Clusters otimizados para os 12 ativos selecionados
        RetailCluster: ["BTC", "ETH", "DOGE", "TON"]  # Ativos populares
        InstitutionalCluster: ["BTC", "ETH", "SOL", "AVAX", "LINK", "AAVE"]  # Blue chips
        MomentumQuant: ["BTC", "ETH", "BNB", "SOL", "XRP", "ADA"]  # Alta liquidez
        DeFiCluster: ["LINK", "AAVE", "ARB"]  # DeFi e Layer 2
        LayerOneCluster: ["BTC", "ETH", "SOL", "ADA", "AVAX"]  # Blockchains principais
        AltcoinCluster: ["DOGE", "TON", "ARB"]  # Altcoins estratégicas
    
    # Parâmetros TSVF para scalping
    tsvf_parameters:
      vector_size: 50             # Menor para responsividade
      alpha: 0.4
      gamma: 0.15
      c_entropy: 0.1
      c_coherence: 0.05
      window_size: 20             # Janela menor

# Configuração do Sistema de Trading
trading_system:
  mode: paper_trading             # SEMPRE paper trading para validação
  exchange: binance
  
  # Símbolos otimizados para scalping (12 ativos mais apropriados)
  symbols:
    # TIER 1: Máxima liquidez e volume (Obrigatórios)
    - "BTC/USDT"   # Rei das criptos, máxima liquidez
    - "ETH/USDT"   # Segunda maior, excelente para scalping
    - "BNB/USDT"   # Token da Binance, spreads baixos
    - "SOL/USDT"   # Alta volatilidade, boa liquidez
    
    # TIER 2: Alta qualidade para scalping
    - "XRP/USDT"   # Movimentos rápidos, alta liquidez
    - "LINK/USDT"  # Oracle líder, boa volatilidade
    - "AVAX/USDT"  # Layer 1 sólida, boa para scalping
    - "ADA/USDT"   # Cardano, movimentos consistentes
    
    # TIER 3: Diversificação estratégica
    - "TON/USDT"   # Telegram, alta volatilidade
    - "DOGE/USDT"  # Meme coin líquida, movimentos rápidos
    - "AAVE/USDT"  # DeFi blue chip, boa volatilidade
    - "ARB/USDT"   # Layer 2 popular, liquidez crescente
  
  timeframes:
    - "1m"                       # Timeframe de entrada (peso: 0.3)
    - "5m"                       # Timeframe de confirmação (peso: 0.4)
    - "15m"                      # Timeframe de ondas de hype (peso: 0.6)
    - "1h"                       # Timeframe de tendência principal (peso: 0.8)
  
  # Limites específicos para scalping
  limits:
    max_positions: 2              # Máximo 2 posições simultâneas
    max_position_size_usd: 10.0   # $10 máximo por posição
    min_position_size_usd: 5.0    # $5 mínimo por posição
    max_daily_trades: 20          # Máximo 20 trades por dia
    max_daily_loss: 50.0          # $50 perda máxima diária
    max_total_loss: 100.0         # $100 perda total máxima
    min_trade_interval_seconds: 30 # Mínimo 30s entre trades
    max_drawdown_pct: 5.0         # 5% drawdown máximo
  
  # Configurações de execução otimizadas
  execution:
    order_type: market            # Ordens de mercado para velocidade
    slippage_tolerance: 0.1       # 0.1% slippage tolerado
    timeout_seconds: 5            # Timeout rápido
    retry_attempts: 2
    confirmation_required: false  # Sem confirmação para velocidade
  
  # Gestão de risco para scalping
  risk_management:
    stop_loss_pct: 0.3           # 0.3% stop loss
    take_profit_pct: 0.5         # 0.5% take profit
    trailing_stop: false         # Sem trailing stop
    position_sizing: fixed       # Tamanho fixo de posição
    risk_per_trade_pct: 1.0      # 1% do capital por trade
    max_correlation: 0.7         # Máxima correlação entre posições

# Configuração de Monitoramento
monitoring:
  update_interval_seconds: 30     # YAA-AJUSTE: Aumentado de 20s para 30s para ciclos de análise mais espaçados
  log_trades: true
  save_metrics: true
  metrics_file: "logs/fwh_scalp_metrics.json"
  performance_tracking: true
  
  # Métricas específicas para scalping
  scalping_metrics:
    track_latency: true
    track_slippage: true
    track_fill_rate: true
    track_win_rate: true
    track_avg_trade_duration: true
    track_profit_factor: true
  
  # Alertas
  alerts:
    max_drawdown_alert: 3.0       # Alerta com 3% de drawdown
    daily_loss_alert: 30.0        # Alerta com $30 de perda diária
    low_win_rate_alert: 40.0      # Alerta se win rate < 40%

# Configuração de Dados de Mercado
market_data:
  # Configurações para alta frequência
  update_frequency: 6             # YAA-FIX: Aumentado de 1s para 6s para respeitar rate limit
  buffer_size: 100               # Buffer de 100 períodos
  cache_enabled: true             # YAA-FIX: Habilitar cache inteligente
  max_retries: 2                  # YAA-FIX: Máximo 2 tentativas por chamada
  
  # Filtros de qualidade para scalping
  quality_filters:
    min_volume_usd: 1000000       # Volume mínimo $1M
    max_spread_pct: 0.05          # Spread máximo 0.05%
    min_price_movement: 0.01      # Movimento mínimo 0.01%
  
  # Configurações de latência
  latency:
    target_latency_ms: 50         # Latência alvo 50ms
    max_latency_ms: 200           # Latência máxima 200ms
    timeout_ms: 1000              # Timeout 1 segundo

# Configuração de Backtesting
backtesting:
  enabled: true
  
  # Período de teste
  start_date: "2024-01-01"
  end_date: "2024-12-31"
  
  # Capital inicial
  initial_capital: 1000.0
  
  # Configurações específicas
  commission: 0.001               # 0.1% comissão
  slippage: 0.0005               # 0.05% slippage
  
  # Métricas a calcular
  metrics:
    - sharpe_ratio
    - max_drawdown
    - win_rate
    - profit_factor
    - calmar_ratio
    - sortino_ratio
    - avg_trade_duration
    - trades_per_day

# Configuração de Logging
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Arquivos de log
  files:
    main: "logs/fwh_scalp_trading.log"
    trades: "logs/fwh_scalp_trades.log"
    metrics: "logs/fwh_scalp_metrics.log"
    errors: "logs/fwh_scalp_errors.log"
  
  # Rotação de logs
  rotation:
    max_size_mb: 10
    backup_count: 5

# Configuração de Segurança
security:
  # Limites de emergência
  emergency_stop:
    max_daily_loss_pct: 5.0       # Para com 5% de perda diária
    max_consecutive_losses: 5     # Para com 5 perdas consecutivas
    min_account_balance: 900.0    # Para se saldo < $900
  
  # Validações
  validations:
    check_balance: true
    check_positions: true
    check_orders: true
    check_connectivity: true
  
  # Timeouts
  timeouts:
    order_timeout: 10             # 10s timeout para ordens
    data_timeout: 5               # 5s timeout para dados
    connection_timeout: 30        # 30s timeout para conexão

# Configuração de Performance
performance:
  # Otimizações
  async_execution: true
  parallel_analysis: true
  cache_enabled: true
  
  # Limites de recursos
  max_memory_mb: 512
  max_cpu_percent: 50
  
  # Configurações de threading
  max_workers: 4
  thread_pool_size: 8

# Configuração de Desenvolvimento
development:
  debug_mode: false
  verbose_logging: false
  save_debug_data: true
  
  # Simulação
  simulation:
    enabled: true
    realistic_latency: true
    realistic_slippage: true
    market_impact: true

# Configuração de Produção
production:
  # Validações adicionais
  strict_validation: true
  double_check_orders: true
  
  # Monitoramento
  health_checks: true
  performance_monitoring: true
  
  # Backup
  auto_backup: true
  backup_interval_hours: 6