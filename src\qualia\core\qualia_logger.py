"""
Sistema de logging unificado para o Universo QUALIA.

Este módulo implementa um mecanismo centralizado para registro de eventos do QUALIA,
permitindo que todos os componentes utilizem uma interface consistente para logging.
"""

import logging
from logging.handlers import RotatingFileHandler
import threading
from ..utils.logger import get_logger
import json
import os
import pandas as pd
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Union
import uuid
from pathlib import Path
from ..utils.custom_logging import load_logging_config, DEFAULT_CONFIG_PATH
from ..config import logging as config_logging
from ..utils.persistence import convert_to_serializable


class QUALIALogger:
    """
    Sistema de logging unificado para o Universo QUALIA.
    Centraliza o registro de eventos, decisões e estados do sistema.
    """

    def __init__(
        self,
        log_dir: str | None = None,
        console_level: int = logging.INFO,
        file_level: int = logging.DEBUG,
        config_path: Path | str | None = None,
    ):
        """
        Inicializa o sistema de logging do QUALIA.

        Args:
            log_dir: Diretório para armazenar os logs. Quando ``None`` utiliza
                ``config.logging.log_dir``
            console_level: Nível de detalhe para console
            file_level: Nível de detalhe para arquivos
        """
        cfg = load_logging_config(config_path or DEFAULT_CONFIG_PATH)

        if log_dir is None:
            log_dir = config_logging.log_dir

        handlers_cfg = cfg.get("handlers", {})
        console_cfg = handlers_cfg.get("console", {})
        file_cfg = handlers_cfg.get("rotating_file", {})

        console_level = getattr(
            logging,
            console_cfg.get("level", logging.getLevelName(console_level)).upper(),
        )
        file_level = getattr(
            logging, file_cfg.get("level", logging.getLevelName(file_level)).upper()
        )

        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)

        self.logger = get_logger(__name__)
        base_level = getattr(
            logging, cfg.get("log_level", "INFO").upper(), logging.INFO
        )
        # self.logger.setLevel(base_level)
        self.logger.propagate = False

        for handler in list(self.logger.handlers):
            self.logger.removeHandler(handler)

        if console_cfg is not None:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(console_level)
            console_format = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            console_handler.setFormatter(console_format)
            self.logger.addHandler(console_handler)

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        file_name = file_cfg.get("filename", f"{log_dir}/qualia_{timestamp}.log")
        Path(file_name).parent.mkdir(parents=True, exist_ok=True)
        file_handler = RotatingFileHandler(
            file_name,
            maxBytes=int(file_cfg.get("maxBytes", 1_048_576)),
            backupCount=int(file_cfg.get("backupCount", 3)),
        )
        file_handler.setLevel(file_level)
        file_format = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        file_handler.setFormatter(file_format)
        self.logger.addHandler(file_handler)

        # Inicializar armazenamento de eventos
        self.events_file = f"{log_dir}/qualia_events_{timestamp}.jsonl"
        self.events = []

        self.logger.info("Sistema de logging QUALIA inicializado")

    # Métodos de acesso direto ao logger subjacente -----------------------
    def debug(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Registra mensagem de debug."""
        self.logger.debug(msg, *args, **kwargs)

    def info(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Registra mensagem de informação."""
        self.logger.info(msg, *args, **kwargs)

    def warning(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Registra alerta."""
        self.logger.warning(msg, *args, **kwargs)

    def error(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Registra erro."""
        self.logger.error(msg, *args, **kwargs)

    def critical(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Registra falha crítica."""
        self.logger.critical(msg, *args, **kwargs)

    def exception(self, msg: str, *args: Any, **kwargs: Any) -> None:
        """Registra exceção com traceback."""
        self.logger.exception(msg, *args, **kwargs)

    def __getattr__(self, name: str) -> Any:  # pragma: no cover - delegação
        """Delegar atributos desconhecidos para ``self.logger``."""
        return getattr(self.logger, name)

    def log_event(
        self,
        event_type: str,
        payload: Dict[str, Any],
        source: str = "qualia",
        level: str = "info",
    ) -> str:
        """
        Registra um evento no sistema QUALIA.

        Args:
            event_type: Tipo do evento (decision, metacognition, etc)
            payload: Dados do evento
            source: Componente fonte do evento
            level: Nível de log (debug, info, warning, error)

        Returns:
            ID do evento registrado
        """
        event_id = payload.get("id", str(uuid.uuid4()))

        # Garantir timestamp consistente em formato ISO
        if "timestamp" not in payload:
            payload["timestamp"] = pd.Timestamp.now().isoformat()
        elif isinstance(payload["timestamp"], datetime):
            payload["timestamp"] = pd.Timestamp(payload["timestamp"]).isoformat()
        elif not isinstance(payload["timestamp"], str):
            payload["timestamp"] = pd.Timestamp(payload["timestamp"]).isoformat()

        event = {
            "id": event_id,
            "type": event_type,
            "source": source,
            "level": level,
            "timestamp": payload.get("timestamp", pd.Timestamp.now().isoformat()),
            "payload": payload,
        }

        # Registrar no log tradicional
        log_method = getattr(self.logger, level, self.logger.info)
        log_method(f"{event_type} ({source}): {payload.get('description', event_id)}")

        # Armazenar evento
        self.events.append(event)

        # Persistir em arquivo
        try:
            serializable = convert_to_serializable(event)
            with open(self.events_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(serializable) + "\n")
        except Exception as e:
            self.logger.error(f"Erro ao persistir evento: {e}")

        return event_id

    def log_decision(
        self,
        decision: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
        quantum_state: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Registra uma decisão de trading.

        Args:
            decision: Dados da decisão
            context: Contexto da decisão (opcional)
            quantum_state: Estado quântico associado (opcional)

        Returns:
            ID da decisão registrada
        """
        # Garantir que temos um ID único
        decision_id = decision.get("id", str(uuid.uuid4()))
        decision["id"] = decision_id

        # Garantir timestamp consistente
        if "timestamp" not in decision:
            decision["timestamp"] = pd.Timestamp.now().isoformat()
        elif isinstance(decision["timestamp"], datetime):
            decision["timestamp"] = pd.Timestamp(decision["timestamp"]).isoformat()
        elif not isinstance(decision["timestamp"], str):
            decision["timestamp"] = pd.Timestamp(decision["timestamp"]).isoformat()

        payload = {
            "id": decision_id,
            "decision": decision,
            "timestamp": decision["timestamp"],
        }

        if context:
            payload["context"] = context

        if quantum_state:
            payload["quantum_state"] = quantum_state

        return self.log_event(
            event_type="trading_decision",
            payload=payload,
            source="trading_engine",
            level="info",
        )

    def log_outcome(
        self, decision_id: str, outcome: str, performance: Dict[str, Any]
    ) -> str:
        """
        Registra o resultado de uma decisão de trading.

        Args:
            decision_id: ID da decisão original
            outcome: Resultado (profitable, loss, etc)
            performance: Métricas de desempenho

        Returns:
            ID do evento de resultado
        """
        payload = {
            "id": str(uuid.uuid4()),
            "decision_id": decision_id,
            "outcome": outcome,
            "performance": performance,
            "timestamp": pd.Timestamp.now().isoformat(),
        }

        return self.log_event(
            event_type="trading_outcome",
            payload=payload,
            source="trading_engine",
            level="info",
        )

    def log_metacognition(
        self, insights: List[Dict[str, Any]], adjustments: List[Dict[str, Any]]
    ) -> str:
        """
        Registra um ciclo de metacognição.

        Args:
            insights: Insights gerados pelo ciclo
            adjustments: Ajustes aplicados pelo ciclo

        Returns:
            ID do evento de metacognição
        """
        payload = {
            "id": str(uuid.uuid4()),
            "insights_count": len(insights),
            "adjustments_count": len(adjustments),
            "insights": insights,
            "adjustments": adjustments,
            "timestamp": pd.Timestamp.now().isoformat(),
        }

        return self.log_event(
            event_type="metacognition_cycle",
            payload=payload,
            source="metacognition_system",
            level="info",
        )

    def log_quantum_state(
        self,
        state_data: Dict[str, Any],
        description: str = "Estado quântico atualizado",
    ) -> str:
        """
        Registra um estado quântico do sistema QUALIA.

        Args:
            state_data: Dados do estado quântico
            description: Descrição do evento

        Returns:
            ID do evento de estado quântico
        """
        payload = {
            "id": str(uuid.uuid4()),
            "state": state_data,
            "description": description,
            "timestamp": pd.Timestamp.now().isoformat(),
        }

        return self.log_event(
            event_type="quantum_state",
            payload=payload,
            source="qualia_universe",
            level="debug",
        )

    def get_events(
        self,
        event_type: Optional[str] = None,
        source: Optional[str] = None,
        start_time: Optional[Union[str, datetime, pd.Timestamp]] = None,
        end_time: Optional[Union[str, datetime, pd.Timestamp]] = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        Recupera eventos do log.

        Args:
            event_type: Filtrar por tipo de evento
            source: Filtrar por fonte
            start_time: Tempo inicial para filtro
            end_time: Tempo final para filtro
            limit: Número máximo de eventos a retornar

        Returns:
            Lista de eventos filtrados
        """
        # Converter timestamps para formato ISO se necessário
        if start_time and not isinstance(start_time, str):
            start_time = pd.Timestamp(start_time).isoformat()

        if end_time and not isinstance(end_time, str):
            end_time = pd.Timestamp(end_time).isoformat()

        # Filtrar eventos
        filtered = self.events

        if event_type:
            filtered = [e for e in filtered if e["type"] == event_type]

        if source:
            filtered = [e for e in filtered if e["source"] == source]

        if start_time:
            filtered = [e for e in filtered if e["timestamp"] >= start_time]

        if end_time:
            filtered = [e for e in filtered if e["timestamp"] <= end_time]

        # Ordenar por timestamp e limitar
        return sorted(filtered, key=lambda e: e["timestamp"], reverse=True)[:limit]

    def get_decisions_with_outcomes(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Recupera decisões que possuem resultados registrados.

        Args:
            limit: Número máximo de decisões a retornar

        Returns:
            Lista de decisões com seus resultados
        """
        # Obter todas as decisões
        decisions = {
            e["payload"]["id"]: e["payload"]
            for e in self.events
            if e["type"] == "trading_decision"
        }

        # Obter todos os resultados
        outcomes = [e["payload"] for e in self.events if e["type"] == "trading_outcome"]

        # Associar resultados às decisões
        decisions_with_outcomes = []
        for outcome in outcomes:
            decision_id = outcome["decision_id"]
            if decision_id in decisions:
                # Criar cópia da decisão e adicionar resultado
                decision = decisions[decision_id].copy()
                decision["outcome"] = outcome["outcome"]
                decision["performance"] = outcome["performance"]
                decisions_with_outcomes.append(decision)

        # Ordenar por timestamp e limitar
        return sorted(
            decisions_with_outcomes, key=lambda d: d["timestamp"], reverse=True
        )[:limit]


# Instância global do logger
_instance = None
# Lock para inicialização thread-safe
_instance_lock = threading.Lock()


def get_event_logger() -> QUALIALogger:
    """Retorna a instância global do ``QUALIALogger`` de eventos.

    Em ambientes multi-thread, chame esta função uma vez no início do
    programa para garantir que o logger esteja pronto para uso.
    """

    global _instance
    if _instance is None:
        with _instance_lock:
            if _instance is None:
                _instance = QUALIALogger()
    return _instance


def log_event(
    event_type: str,
    payload: Dict[str, Any],
    source: str = "qualia",
    level: str = "info",
) -> str:
    """Função auxiliar que registra um evento no logger global."""

    return get_event_logger().log_event(event_type, payload, source, level)
