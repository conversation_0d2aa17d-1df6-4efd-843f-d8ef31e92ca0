#!/usr/bin/env python3
"""
Script Simplificado para Executar Backtest Abrangente da Estratégia FWH

Este script fornece uma interface simples para executar o sistema de backtest
abrangente com diferentes configurações e cenários.

Uso:
    python run_comprehensive_backtest.py --config quick
    python run_comprehensive_backtest.py --config full
    python run_comprehensive_backtest.py --config custom --symbols BTC/USDT,ETH/USDT
"""

import sys
import os
import asyncio
import argparse
from datetime import datetime, timedelta
from pathlib import Path

# Adicionar o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from comprehensive_fwh_backtest import ComprehensiveFWHBacktester, BacktestConfig
    from qualia.utils.logger import get_logger
except ImportError as e:
    print(f"Erro ao importar módulos: {e}")
    sys.exit(1)

logger = get_logger(__name__)

def create_quick_config() -> BacktestConfig:
    """Configuração rápida para teste inicial (últimos 30 dias)"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    return BacktestConfig(
        start_date=start_date.strftime("%Y-%m-%d"),
        end_date=end_date.strftime("%Y-%m-%d"),
        symbols=["BTC/USDT", "ETH/USDT"],
        timeframes=["5m", "15m"],  # Timeframes menos agressivos
        initial_capital=1000.0,
        trading_fee_pct=0.0005,  # 0.05% fee (mais realista com desconto BNB)
        slippage_bps=1.0,        # 1 basis point de slippage
        enable_optimization=False,
        optimization_trials=20,
        generate_plots=True,
        output_dir="improved_backtest_results"
    )

def create_full_config() -> BacktestConfig:
    """Configuração completa para análise abrangente (último ano)"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    
    return BacktestConfig(
        start_date=start_date.strftime("%Y-%m-%d"),
        end_date=end_date.strftime("%Y-%m-%d"),
        symbols=["BTC/USDT", "ETH/USDT", "BNB/USDT", "SOL/USDT", "ADA/USDT"],
        timeframes=["1m", "5m", "15m", "1h"],
        initial_capital=10000.0,
        enable_optimization=True,
        optimization_trials=100,
        optimization_timeout_hours=3.0,
        enable_walk_forward=True,
        generate_plots=True,
        output_dir="full_backtest_results"
    )

def create_custom_config(args) -> BacktestConfig:
    """Configuração customizada baseada nos argumentos"""
    # Datas padrão se não especificadas
    if args.start_date:
        start_date = args.start_date
    else:
        start_date = (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d")
    
    if args.end_date:
        end_date = args.end_date
    else:
        end_date = datetime.now().strftime("%Y-%m-%d")
    
    # Símbolos
    if args.symbols:
        symbols = [s.strip() for s in args.symbols.split(',')]
    else:
        symbols = ["BTC/USDT", "ETH/USDT", "BNB/USDT"]
    
    # Timeframes
    if args.timeframes:
        timeframes = [tf.strip() for tf in args.timeframes.split(',')]
    else:
        timeframes = ["1m", "5m"]
    
    return BacktestConfig(
        start_date=start_date,
        end_date=end_date,
        symbols=symbols,
        timeframes=timeframes,
        initial_capital=args.capital or 5000.0,
        enable_optimization=args.optimize,
        optimization_trials=args.trials or 50,
        generate_plots=True,
        output_dir=args.output or "custom_backtest_results"
    )

async def run_backtest_scenario(config: BacktestConfig, scenario_name: str):
    """Executa um cenário de backtest"""
    print(f"\n🚀 Executando Cenário: {scenario_name}")
    print("=" * 60)
    print(f"📅 Período: {config.start_date} até {config.end_date}")
    print(f"💰 Capital Inicial: ${config.initial_capital:,.2f}")
    print(f"📊 Símbolos: {', '.join(config.symbols)}")
    print(f"⏰ Timeframes: {', '.join(config.timeframes)}")
    print(f"🎯 Otimização: {'Habilitada' if config.enable_optimization else 'Desabilitada'}")
    print(f"📁 Saída: {config.output_dir}/")
    print("-" * 60)
    
    try:
        # Criar e executar backtester
        backtester = ComprehensiveFWHBacktester(config)
        results = await backtester.run_comprehensive_backtest()
        
        # Exibir resumo dos resultados
        print_results_summary(results, scenario_name)
        
        return results
        
    except Exception as e:
        logger.error(f"Erro no cenário {scenario_name}: {e}")
        print(f"❌ Erro no cenário {scenario_name}: {e}")
        raise

def print_results_summary(results: dict, scenario_name: str):
    """Exibe resumo dos resultados"""
    print(f"\n✅ Cenário '{scenario_name}' Concluído!")
    print("=" * 60)
    
    general_stats = results.get('general_statistics', {})
    
    print(f"📊 Estatísticas Gerais:")
    print(f"   • Combinações Testadas: {general_stats.get('total_combinations', 0)}")
    print(f"   • Combinações Lucrativas: {general_stats.get('profitable_combinations', 0)}")
    print(f"   • Sharpe Ratio Médio: {general_stats.get('avg_sharpe_ratio', 0):.3f}")
    print(f"   • Melhor Sharpe Ratio: {general_stats.get('best_sharpe_ratio', 0):.3f}")
    print(f"   • Retorno Médio: {general_stats.get('avg_return_pct', 0):.2f}%")
    print(f"   • Win Rate Médio: {general_stats.get('avg_win_rate', 0)*100:.1f}%")
    print(f"   • Drawdown Médio: {general_stats.get('avg_max_drawdown', 0):.2f}%")
    
    # Top performers
    top_performers = results.get('top_performers', [])
    if top_performers:
        print(f"\n🏆 Top 5 Performers:")
        for i, (key, result) in enumerate(top_performers[:5], 1):
            print(f"   {i}. {key}")
            print(f"      Sharpe: {result['sharpe_ratio']:.3f} | "
                  f"Retorno: {result['total_return_pct']:.2f}% | "
                  f"Win Rate: {result['win_rate']*100:.1f}%")
    
    # Recomendações
    recommendations = results.get('recommendations', {})
    if recommendations:
        print(f"\n💡 Recomendações:")
        
        best_symbols = recommendations.get('best_symbols', [])
        if best_symbols:
            print(f"   • Melhores Símbolos: {', '.join([s[0] for s in best_symbols[:3]])}")
        
        best_timeframes = recommendations.get('best_timeframes', [])
        if best_timeframes:
            print(f"   • Melhores Timeframes: {', '.join([tf[0] for tf in best_timeframes[:3]])}")
        
        warnings = recommendations.get('risk_warnings', [])
        if warnings:
            print(f"   ⚠️  Avisos de Risco:")
            for warning in warnings:
                print(f"      - {warning}")
    
    print(f"\n📁 Relatórios detalhados salvos em: {results['backtest_summary']['config']['output_dir']}/")

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description="Sistema de Backtest Abrangente FWH")
    
    parser.add_argument('--config', choices=['quick', 'full', 'custom'], default='quick',
                       help='Tipo de configuração (default: quick)')
    
    # Argumentos para configuração customizada
    parser.add_argument('--symbols', type=str,
                       help='Símbolos separados por vírgula (ex: BTC/USDT,ETH/USDT)')
    parser.add_argument('--timeframes', type=str,
                       help='Timeframes separados por vírgula (ex: 1m,5m,15m)')
    parser.add_argument('--start-date', type=str,
                       help='Data de início (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str,
                       help='Data de fim (YYYY-MM-DD)')
    parser.add_argument('--capital', type=float,
                       help='Capital inicial')
    parser.add_argument('--optimize', action='store_true',
                       help='Habilitar otimização de parâmetros')
    parser.add_argument('--trials', type=int,
                       help='Número de trials para otimização')
    parser.add_argument('--output', type=str,
                       help='Diretório de saída')
    
    # Cenários predefinidos
    parser.add_argument('--scenario', choices=['comparison', 'stress_test', 'sensitivity'],
                       help='Executar cenário especial')
    
    args = parser.parse_args()
    
    print("🎯 Sistema de Backtest Abrangente - Estratégia FWH")
    print("=" * 60)
    
    try:
        if args.scenario == 'comparison':
            # Executar comparação entre configurações
            asyncio.run(run_comparison_scenario())
        elif args.scenario == 'stress_test':
            # Executar teste de stress
            asyncio.run(run_stress_test_scenario())
        elif args.scenario == 'sensitivity':
            # Executar análise de sensibilidade
            asyncio.run(run_sensitivity_scenario())
        else:
            # Executar configuração normal
            if args.config == 'quick':
                config = create_quick_config()
            elif args.config == 'full':
                config = create_full_config()
            else:  # custom
                config = create_custom_config(args)
            
            asyncio.run(run_backtest_scenario(config, args.config.upper()))
    
    except KeyboardInterrupt:
        print("\n⏹️  Backtest interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro durante execução: {e}")
        sys.exit(1)

async def run_comparison_scenario():
    """Executa cenário de comparação entre diferentes configurações"""
    print("🔄 Executando Cenário de Comparação")
    
    configs = {
        "Conservador": BacktestConfig(
            symbols=["BTC/USDT", "ETH/USDT"],
            timeframes=["5m", "15m"],
            initial_capital=5000.0,
            enable_optimization=False,
            output_dir="comparison_conservative"
        ),
        "Agressivo": BacktestConfig(
            symbols=["BTC/USDT", "ETH/USDT", "BNB/USDT", "SOL/USDT"],
            timeframes=["1m", "5m"],
            initial_capital=5000.0,
            enable_optimization=True,
            optimization_trials=30,
            output_dir="comparison_aggressive"
        )
    }
    
    results = {}
    for name, config in configs.items():
        results[name] = await run_backtest_scenario(config, name)
    
    # Comparar resultados
    print("\n📊 Comparação de Resultados:")
    for name, result in results.items():
        stats = result['general_statistics']
        print(f"{name}: Sharpe={stats.get('best_sharpe_ratio', 0):.3f}, "
              f"Retorno={stats.get('avg_return_pct', 0):.2f}%")

async def run_stress_test_scenario():
    """Executa cenário de teste de stress"""
    print("⚡ Executando Teste de Stress")
    
    # Configuração com condições adversas
    config = BacktestConfig(
        symbols=["BTC/USDT", "ETH/USDT"],
        timeframes=["1m"],
        initial_capital=1000.0,
        trading_fee_pct=0.002,  # Fees mais altas
        slippage_bps=5.0,       # Slippage maior
        enable_optimization=False,
        output_dir="stress_test_results"
    )
    
    await run_backtest_scenario(config, "STRESS_TEST")

async def run_sensitivity_scenario():
    """Executa análise de sensibilidade de parâmetros"""
    print("🎛️  Executando Análise de Sensibilidade")
    
    config = BacktestConfig(
        symbols=["BTC/USDT"],
        timeframes=["5m"],
        initial_capital=2000.0,
        enable_optimization=True,
        optimization_trials=50,
        output_dir="sensitivity_analysis"
    )
    
    await run_backtest_scenario(config, "SENSITIVITY_ANALYSIS")

if __name__ == "__main__":
    main()
