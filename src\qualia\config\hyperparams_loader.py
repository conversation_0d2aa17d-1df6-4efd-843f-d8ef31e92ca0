"""
QUALIA Hyperparameters Loader - Fonte Única de Verdade
=====================================================

YAA REFINEMENT: Loader centralizado para hiperparâmetros críticos do sistema QUALIA.
Implementa precedência de configuração: arquivo base < env vars < CLI args < API override.

Este módulo elimina a duplicação de parâmetros como price_amp, news_amp, min_confidence
espalhados por múltiplos arquivos YAML, consolidando-os em uma fonte única de verdade.
"""

from __future__ import annotations

import os
import argparse
import logging
import json
import requests
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field

import yaml

logger = logging.getLogger(__name__)


@dataclass
class HyperParams:
    """Dataclass para hiperparâmetros consolidados do QUALIA."""
    
    # Amplificação
    price_amplification: float = 5.0
    news_amplification: float = 4.0
    pattern_threshold: float = 0.3
    
    # Confiança
    min_confidence: float = 0.6
    
    # Limites de segurança
    min_amplification: float = 1.0
    max_amplification: float = 10.0
    min_pattern_threshold: float = 0.2
    max_pattern_threshold: float = 0.8
    
    # Aprendizado
    learning_rate: float = 0.1
    history_size: int = 100
    calibration_interval: int = 300
    min_samples_for_calibration: int = 10
    
    # Perfil de risco ativo
    risk_profile: str = "moderate"
    
    # Metadados
    source: str = field(default="default")
    overrides: Dict[str, str] = field(default_factory=dict)
    
    def validate(self, send_alerts: bool = True) -> None:
        """Valida os hiperparâmetros contra as regras definidas."""
        errors = []
        warnings = []

        # Validação de bounds críticos
        if not (self.min_amplification <= self.price_amplification <= self.max_amplification):
            error_msg = f"price_amplification ({self.price_amplification}) fora dos limites CRÍTICOS [{self.min_amplification}, {self.max_amplification}]"
            errors.append(error_msg)
            if send_alerts:
                self._send_safety_alert("CRITICAL_BOUNDS_VIOLATION", error_msg)

        if not (self.min_amplification <= self.news_amplification <= self.max_amplification):
            error_msg = f"news_amplification ({self.news_amplification}) fora dos limites CRÍTICOS [{self.min_amplification}, {self.max_amplification}]"
            errors.append(error_msg)
            if send_alerts:
                self._send_safety_alert("CRITICAL_BOUNDS_VIOLATION", error_msg)

        if not (0.0 <= self.min_confidence <= 1.0):
            error_msg = f"min_confidence ({self.min_confidence}) deve estar entre 0.0 e 1.0"
            errors.append(error_msg)
            if send_alerts:
                self._send_safety_alert("CRITICAL_BOUNDS_VIOLATION", error_msg)

        if not (self.min_pattern_threshold <= self.pattern_threshold <= self.max_pattern_threshold):
            error_msg = f"pattern_threshold ({self.pattern_threshold}) fora dos limites [{self.min_pattern_threshold}, {self.max_pattern_threshold}]"
            errors.append(error_msg)

        # Validação de limites de alerta (não críticos, mas geram warnings)
        if self.price_amplification > 8.0:
            warning_msg = f"price_amplification ({self.price_amplification}) está muito alto (>8.0) - risco elevado"
            warnings.append(warning_msg)
            if send_alerts:
                self._send_safety_alert("HIGH_RISK_WARNING", warning_msg)

        if self.news_amplification > 12.0:
            warning_msg = f"news_amplification ({self.news_amplification}) está muito alto (>12.0) - risco elevado"
            warnings.append(warning_msg)
            if send_alerts:
                self._send_safety_alert("HIGH_RISK_WARNING", warning_msg)

        # Validação de consistência lógica (relaxada para permitir otimizações)
        if self.price_amplification < self.news_amplification * 0.05:  # Muito mais permissivo
            warning_msg = "price_amplification extremamente menor que news_amplification - configuração incomum"
            warnings.append(warning_msg)

        # Log warnings
        for warning in warnings:
            logger.warning(f"⚠️ {warning}")

        if errors:
            raise ValueError(f"Validação de hiperparâmetros falhou: {'; '.join(errors)}")

    def _send_safety_alert(self, alert_type: str, message: str) -> None:
        """Envia alerta de segurança via Slack se configurado."""
        try:
            slack_webhook = os.getenv("QUALIA_SLACK_WEBHOOK")
            if not slack_webhook:
                logger.debug("QUALIA_SLACK_WEBHOOK não configurado - pulando alerta Slack")
                return

            emoji = "🚨" if alert_type == "CRITICAL_BOUNDS_VIOLATION" else "⚠️"
            color = "danger" if alert_type == "CRITICAL_BOUNDS_VIOLATION" else "warning"

            payload = {
                "attachments": [{
                    "color": color,
                    "title": f"{emoji} QUALIA Safety Alert - {alert_type}",
                    "text": message,
                    "fields": [
                        {"title": "Sistema", "value": "QUALIA Trading System", "short": True},
                        {"title": "Timestamp", "value": f"<t:{int(__import__('time').time())}:F>", "short": True},
                        {"title": "Fonte", "value": self.source, "short": True},
                        {"title": "Overrides Ativos", "value": str(self.overrides), "short": False}
                    ],
                    "footer": "QUALIA Fail-safe System"
                }]
            }

            response = requests.post(slack_webhook, json=payload, timeout=5)
            if response.status_code == 200:
                logger.info(f"✅ Alerta Slack enviado: {alert_type}")
            else:
                logger.warning(f"⚠️ Falha ao enviar alerta Slack: {response.status_code}")

        except Exception as e:
            logger.warning(f"⚠️ Erro ao enviar alerta Slack: {e}")


class HyperParamsLoader:
    """
    Loader centralizado para hiperparâmetros QUALIA.
    
    Implementa precedência de configuração:
    1. Valores padrão do arquivo hyperparams.yaml
    2. Variáveis de ambiente (QUALIA_*)
    3. Argumentos CLI (--price-amp, etc.)
    4. Override dinâmico via API
    """
    
    def __init__(self, config_path: Optional[str] = None, production_config_path: Optional[str] = None):
        self.config_path = Path(config_path or "qualia/config/hyperparams.yaml")
        self.production_config_path = Path(production_config_path or "config/production_config.json")
        self.base_config: Dict[str, Any] = {}
        self.production_config: Dict[str, Any] = {}
        self.dynamic_overrides: Dict[str, Any] = {}
        self._cached_params: Optional[HyperParams] = None

        self._load_base_config()
        self._load_production_config()
    
    def _load_base_config(self) -> None:
        """Carrega configuração base do arquivo YAML."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.base_config = yaml.safe_load(f) or {}
                logger.info(f"✅ Hiperparâmetros base carregados de: {self.config_path}")
            else:
                logger.warning(f"⚠️ Arquivo de hiperparâmetros não encontrado: {self.config_path}")
                self.base_config = {}
        except Exception as e:
            logger.error(f"❌ Erro ao carregar hiperparâmetros: {e}")
            self.base_config = {}

    def _load_production_config(self):
        """Carrega configuração de produção com parâmetros otimizados."""
        try:
            if self.production_config_path.exists():
                with open(self.production_config_path, 'r', encoding='utf-8') as f:
                    self.production_config = json.load(f)
                logger.info(f"✅ Configuração de produção carregada: {self.production_config_path}")
            else:
                logger.info(f"📋 Configuração de produção não encontrada: {self.production_config_path}")
                self.production_config = {}
        except Exception as e:
            logger.error(f"❌ Erro ao carregar configuração de produção: {e}")
            self.production_config = {}
    
    def _get_env_overrides(self) -> Dict[str, Any]:
        """Extrai overrides das variáveis de ambiente."""
        overrides = {}
        env_mapping = self.base_config.get("override_precedence", {}).get("environment_variables", {})
        
        for param, env_var in env_mapping.items():
            value = os.getenv(env_var)
            if value is not None:
                # Converte string para tipo apropriado
                try:
                    if param in ["price_amplification", "news_amplification", "pattern_threshold", "min_confidence", "learning_rate"]:
                        overrides[param] = float(value)
                    elif param in ["history_size", "calibration_interval", "min_samples_for_calibration"]:
                        overrides[param] = int(value)
                    else:
                        overrides[param] = value
                    logger.info(f"🔧 Override de env var: {param} = {overrides[param]} (de {env_var})")
                except ValueError as e:
                    logger.warning(f"⚠️ Valor inválido para {env_var}: {value} - {e}")
        
        return overrides
    
    def _get_cli_overrides(self, args: Optional[argparse.Namespace] = None) -> Dict[str, Any]:
        """Extrai overrides dos argumentos CLI."""
        if args is None:
            return {}
            
        overrides = {}
        cli_mapping = {
            "price_amp": "price_amplification",
            "news_amp": "news_amplification", 
            "min_confidence": "min_confidence",
            "pattern_threshold": "pattern_threshold",
            "risk_profile": "risk_profile"
        }
        
        for cli_arg, param in cli_mapping.items():
            value = getattr(args, cli_arg, None)
            if value is not None:
                overrides[param] = value
                logger.info(f"🔧 Override de CLI: {param} = {value}")
        
        return overrides
    
    def load(self, 
             args: Optional[argparse.Namespace] = None,
             force_reload: bool = False) -> HyperParams:
        """
        Carrega hiperparâmetros com precedência completa.
        
        Args:
            args: Argumentos CLI parseados
            force_reload: Força recarregamento ignorando cache
            
        Returns:
            HyperParams: Hiperparâmetros consolidados e validados
        """
        if self._cached_params is not None and not force_reload:
            return self._cached_params
        
        # 1. Valores base do arquivo
        base_values = self.base_config.get("amplification", {}).get("initial", {})
        confidence_values = self.base_config.get("confidence", {})
        limits = self.base_config.get("amplification", {}).get("limits", {})
        learning = self.base_config.get("amplification", {}).get("learning", {})

        # 1.5. Valores otimizados do production_config.json (prioridade sobre base)
        production_params = self.production_config.get("base_params", {})
        
        # 2. Aplica overrides de variáveis de ambiente
        env_overrides = self._get_env_overrides()
        
        # 3. Aplica overrides de CLI
        cli_overrides = self._get_cli_overrides(args)
        
        # 4. Aplica overrides dinâmicos
        dynamic_overrides = self.dynamic_overrides.copy()
        
        # Constrói parâmetros finais com precedência
        final_params = {}
        
        # Base (com prioridade para parâmetros otimizados de produção)
        final_params.update({
            "price_amplification": production_params.get("price_amplification", base_values.get("price_amplification", 5.0)),
            "news_amplification": production_params.get("news_amplification", base_values.get("news_amplification", 4.0)),
            "pattern_threshold": production_params.get("pattern_threshold", base_values.get("pattern_threshold", 0.3)),
            "min_confidence": production_params.get("min_confidence", confidence_values.get("min_confidence", 0.6)),
            "min_amplification": limits.get("min_amplification", 1.0),
            "max_amplification": limits.get("max_amplification", 10.0),
            "min_pattern_threshold": limits.get("min_pattern_threshold", 0.2),
            "max_pattern_threshold": limits.get("max_pattern_threshold", 0.8),
            "learning_rate": learning.get("rate", 0.1),
            "history_size": learning.get("history_size", 100),
            "calibration_interval": learning.get("calibration_interval", 300),
            "min_samples_for_calibration": learning.get("min_samples_for_calibration", 10),
            "risk_profile": "moderate"
        })

        # Log dos parâmetros otimizados aplicados
        if production_params:
            logger.info(f"🔥 Aplicando parâmetros otimizados de produção:")
            for key, value in production_params.items():
                if key in final_params:
                    logger.info(f"   • {key}: {value}")

        # Log das descobertas de otimização se disponíveis
        discoveries = self.production_config.get("optimization_discoveries", {})
        if discoveries:
            logger.info(f"📊 Descobertas de otimização incorporadas:")
            for key, value in discoveries.items():
                logger.info(f"   • {key}: {value}")
        
        # Aplica overrides em ordem de precedência
        final_params.update(env_overrides)
        final_params.update(cli_overrides)
        final_params.update(dynamic_overrides)
        
        # Aplica perfil de risco se especificado (mas não sobrescreve parâmetros otimizados)
        risk_profile = final_params.get("risk_profile", "moderate")
        if risk_profile in self.base_config.get("risk_profiles", {}):
            profile_params = self.base_config["risk_profiles"][risk_profile]
            # Aplica apenas se não foi sobrescrito explicitamente (incluindo production_params)
            for key, value in profile_params.items():
                if (key not in production_params and
                    key not in env_overrides and
                    key not in cli_overrides and
                    key not in dynamic_overrides):
                    final_params[key] = value
            logger.info(f"📊 Aplicado perfil de risco: {risk_profile} (preservando parâmetros otimizados)")
        
        # Cria e valida hiperparâmetros
        try:
            hyperparams = HyperParams(**final_params)
            production_count = len(production_params) if production_params else 0
            hyperparams.source = f"base+production({production_count})+env({len(env_overrides)})+cli({len(cli_overrides)})+dynamic({len(dynamic_overrides)})"
            hyperparams.overrides = {**production_params, **env_overrides, **cli_overrides, **dynamic_overrides}

            # Enhanced validation with new validator
            try:
                from src.qualia.config.hyperparams_validator import HyperparametersValidator

                enhanced_validator = HyperparametersValidator()
                validation_result = enhanced_validator.validate_hyperparameters(final_params)

                # Log validation results
                if validation_result.warnings:
                    for warning in validation_result.warnings:
                        logger.warning(f"⚠️ Hyperparameter warning: {warning}")

                if validation_result.recommendations:
                    for rec in validation_result.recommendations:
                        logger.info(f"💡 Hyperparameter recommendation: {rec}")

                if not validation_result.is_valid:
                    logger.error("❌ Enhanced validation failed:")
                    for error in validation_result.critical_violations:
                        logger.error(f"   CRITICAL: {error}")
                    # Still proceed with basic validation for backward compatibility

            except ImportError:
                logger.debug("Enhanced validator not available, using basic validation")
            except Exception as e:
                logger.warning(f"Enhanced validation failed: {e}")

            # Basic validation (original)
            hyperparams.validate()
            self._cached_params = hyperparams

            logger.info(f"✅ Hiperparâmetros carregados: price_amp={hyperparams.price_amplification}, "
                       f"news_amp={hyperparams.news_amplification}, min_conf={hyperparams.min_confidence}")

            return hyperparams
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar hiperparâmetros: {e}")
            raise
    
    def set_dynamic_override(self, param: str, value: Any) -> None:
        """Define override dinâmico (maior precedência)."""
        self.dynamic_overrides[param] = value
        self._cached_params = None  # Invalida cache
        logger.info(f"🔧 Override dinâmico definido: {param} = {value}")
    
    def clear_dynamic_overrides(self) -> None:
        """Limpa todos os overrides dinâmicos."""
        self.dynamic_overrides.clear()
        self._cached_params = None
        logger.info("🧹 Overrides dinâmicos limpos")
    
    def get_validation_report(self) -> Dict[str, Any]:
        """Gera relatório de validação dos hiperparâmetros atuais."""
        try:
            params = self.load()
            return {
                "status": "valid",
                "params": params.__dict__,
                "source": params.source,
                "overrides": params.overrides
            }
        except Exception as e:
            return {
                "status": "invalid",
                "error": str(e),
                "base_config_loaded": bool(self.base_config)
            }


# Instância global para uso em todo o sistema
_global_hyperparams_loader: Optional[HyperParamsLoader] = None


def get_global_hyperparams_loader() -> HyperParamsLoader:
    """Retorna instância global do loader de hiperparâmetros."""
    global _global_hyperparams_loader
    if _global_hyperparams_loader is None:
        _global_hyperparams_loader = HyperParamsLoader()
    return _global_hyperparams_loader


def load_hyperparams(args: Optional[argparse.Namespace] = None) -> HyperParams:
    """Função de conveniência para carregar hiperparâmetros."""
    return get_global_hyperparams_loader().load(args)


__all__ = ["HyperParams", "HyperParamsLoader", "get_global_hyperparams_loader", "load_hyperparams"]
