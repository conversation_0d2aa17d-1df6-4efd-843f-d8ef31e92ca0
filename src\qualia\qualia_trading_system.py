# coding: utf-8
"""Real-time trading loop built on the QUALIA framework.

This module maintains a small history of recent error counters used for
health monitoring. When the counters exceed :data:`MAX_ERROR_HISTORY`,
they are trimmed to avoid uncontrolled growth.

Example
-------
>>> import asyncio
>>> from .qualia_trading_system import QUALIARealTimeTrader
>>> from .trading import trading_loops
>>> trader = QUALIARealTimeTrader()
>>> asyncio.run(trading_loops.run_once(trader))
"""

# YAA: Padronizar todas as importações para usar o prefixo 'src.'
from .metacognition import QUALIAMetacognitionTrading, MetacognitiveContext
from .metacognition.metacognition_trading import (
    DecisionContext,
    MetacognitionResult,
)
from .memory import get_qpm_instance, QuantumPatternMemory

try:  # pragma: no cover - dependency may be optional in tests
    from .market.quantum_metrics_calculator import QuantumMetricsCalculator
except Exception:  # pragma: no cover - allow missing heavy deps
    QuantumMetricsCalculator = None
from .market.decision_context import (
    <PERSON><PERSON>ontext,
    ScalpingDecision,
)
from .adaptive_evolution import AdaptiveConsciousnessEvolution
from .common_types import QuantumSignaturePacket, SnapshotPayload
from .core.consciousness import QUALIAConsciousness as QualiaAnalysisCore
from .consciousness.live_feed_integration import LiveFeedIntegration
from .core.universe import QUALIAQuantumUniverse
from .core.qast_core import TradingQASTCore
from .core.simulation_qast_core import SimulationQASTCore
from .risk.manager import QUALIARiskManager, create_risk_manager
from .config.risk_profiles import _FALLBACK_RISK_PROFILE_SETTINGS
from .risk_management.risk_manager_base import QUALIARiskManagerBase
from .market.base_integration import CryptoDataFetcher
from .common.specs import MarketSpec
from .market.symbol_utils import (
    normalize_symbol,
    normalize_symbol_async,
    validate_and_normalize_symbols,
)
from .config import VectorType
from .strategies.strategy_utils import (
    create_strategy_and_qast_engine,
    get_params_dataclass_for_alias,
)
from .strategies.strategy_factory import StrategyFactory
from .strategies.nova_estrategia_qualia import QualiaTSVFStrategy
from .strategies.exceptions import (
    DataRequirementsError,
    InsufficientHistoryError,
)
from .init_utils import setup_core_components
from .utils.logging_config import StructuredFormatter
from .core.position import OpenPosition
from .core.adaptive_history_manager import (
    AdaptiveHistoryManager,
    HistoryRequirement,
    ExchangeCapability,
)

import threading
import psutil
import contextlib

from pathlib import Path
import traceback
import base64
import binascii
from dataclasses import dataclass, field, asdict
from typing import (
    Dict,
    List,
    Any,
    Optional,
    Tuple,
    Union,
    cast,
    Iterator,
    Sequence,
)
from datetime import datetime, timedelta, timezone
import numpy as np
import pandas as pd
import logging
import random
from .utils.logger import get_logger
from .constants import MIN_INITIAL_HISTORY
from .config.settings import market_metrics_enabled
from .utils.metrics import record_metric
from .utils.trading_calculations import get_price_from_ticker
from datadog import DogStatsd
from .utils.logging_config import (
    resolve_log_level,
    apply_log_level,
    logger_throttle,
)
from .persistence import OrderJournal
from .metrics.performance_metrics import aggregate_trade_performance
from .trader import (
    exchange_manager,
    position_manager,
    execution_engine,
    setup_utils,
    position_storage,
    HUDManager,
)
import json
import time
import asyncio
import sys
import ccxt

try:
    AuthenticationError = ccxt.AuthenticationError
except AttributeError:  # pragma: no cover - stubbed ccxt

    class AuthenticationError(Exception):
        """Fallback for missing ccxt.AuthenticationError."""

        pass


import os
import sys
from pathlib import Path

try:  # pragma: no cover - optional dependency
    from opentelemetry import trace
except Exception:
    trace = None

from contextlib import nullcontext
from .utils.tracing import instrument_logger, configure_tracing as _configure_tracing

# Adicionar o diretório raiz ao path para importar scripts
root_path = Path(__file__).parent.parent.parent
if str(root_path) not in sys.path:
    sys.path.insert(0, str(root_path))
sys.modules.setdefault("src.qualia.qualia_trading_system", sys.modules[__name__])

from scripts.check_json import load_strategy_parameters
from . import exchange_setup, fee_management, trading_loops
from .trading.utils import (
    next_candle_time,
    timeframe_to_minutes,
    timeframe_to_milliseconds,
    validate_secret_key,
)
from .config.settings import get_env, settings
from .config import load_trading_defaults
from .init_utils import load_environment
from .utils.logger import setup_logging

# Logger do módulo
logger = get_logger(__name__)
throttled_debug = logger_throttle(logger.debug, interval=30.0)

# Maximum number of error events tracked before counters are trimmed.
MAX_ERROR_HISTORY = settings.max_error_history


def configure_tracing() -> bool:
    """Configure tracing exporter respecting ``QUALIA_NO_DISPLAY``."""

    exporter = os.getenv("QUALIA_TRACING_EXPORTER", "console")
    if os.getenv("QUALIA_NO_DISPLAY"):
        exporter = "console"
    configured = _configure_tracing(service_name="qualia-trader", exporter=exporter)
    if configured:
        instrument_logger(logging.getLogger())
    return configured


class TickerFetchError(Exception):
    """Raised when ticker data cannot be retrieved after retries."""

    pass


@dataclass
class SystemHealthStatus:
    """Track the current health status of the trading loop."""

    ticker_fetch: str = "ok"
    status: str = "ok"
    component_initialization: str = "pending"

    def get(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Provide ``dict``-like access for legacy code."""
        return getattr(self, key, default)


# Carregue as variáveis de ambiente apenas durante o startup da aplicação

import copy
from importlib import resources
from qiskit import (
    QuantumCircuit,
    # Statevector, # YAA: Statevector é de qiskit.quantum_info
)
from qiskit.quantum_info import Statevector  # YAA: Correção do import
import uuid
from functools import partial
from collections import defaultdict

# Caminho raiz do projeto para localizar arquivos de configuração e recursos
PROJECT_ROOT = Path(resources.files("src.qualia")).resolve().parent.parent

# --- Rotinas de segurança -------------------------------------------------


# Importar módulos QUALIA

# Classe para escrita de resultados de trading


class ResultWriter:
    """Escreve resumos de execução em JSON no diretório de resultados."""

    def __init__(self, base_dir: str):
        self.base_dir = base_dir
        from .config import config

        self.results_path = config.runs_json_dir
        os.makedirs(self.results_path, exist_ok=True)

    def write(self, filename: str, data: Any):
        """Grava dados JSON no arquivo especificado."""
        file_path = os.path.join(self.results_path, filename)
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, default=str)

    def write_summary(
        self,
        wallet_state: Dict[str, Any],
        trade_history: List[Dict[str, Any]],
        analysis_results: Optional[Dict[str, Any]] = None,
    ):
        """Compõe e grava o resumo final da execução."""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%dT%H%M%S")
        summary = {
            "wallet_state": wallet_state,
            "trade_history": trade_history,
        }
        if analysis_results is not None:
            summary["analysis_results"] = analysis_results
        filename = f"summary_{timestamp}.json"
        self.write(filename, summary)


# Usar sistema de logging centralizado QUALIA
# logger.propagate = False # YAA: Removido para permitir propagação para handler global configurado em paper_trading.py

# Cores para o console
GREEN = "\033[92m"
RED = "\033[91m"
YELLOW = "\033[93m"
BLUE = "\033[94m"
MAGENTA = "\033[95m"
CYAN = "\033[96m"
BOLD = "\033[1m"
ENDC = "\033[0m"


class QUALIARealTimeTrader:
    """Motor principal para trading em tempo real ou paper trading.

    Inclui segurança de credenciais, logging estruturado e monitoramento de saúde
    do sistema.
    """

    # Defaults used when instance initialization is bypassed in tests
    REDUCE_EXPOSURE_MIN_CONFIDENCE: float = 0.6
    REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES: float = 5.0
    REDUCE_EXPOSURE_CLOSE_SINGLE_POSITION: bool = False
    REDUCE_EXPOSURE_DEFAULT_PCT: float = 50.0
    adaptive_poll: bool = True
    default_poll_interval: float = 60.0
    current_poll_interval: float = 60.0
    _poll_backoff_level: int = 0
    trade_history: List[Dict[str, Any]] = []
    poll_interval_override: Optional[float] = 60.0
    SLEEP_TIME_WARNING_THRESHOLD: int = 3600
    qast_schedule_interval_minutes: float = 0.0
    trade_interval: float = 10.0  # Intervalo mínimo entre trades em segundos
    _DEFAULTS = load_trading_defaults()
    DEFAULT_MARKET_DATA_TIMEOUT: float = float(
        _DEFAULTS.get("market_data_timeout", 120.0)
    )

    def __init__(
        self,
        symbols: List[str],
        timeframes: List[str],
        capital: float,
        risk_profile: str,
        mode: str = "paper_trading",  # "paper_trading" ou "live"
        data_source: str = "kraken",
        duration_seconds: Optional[int] = None,
        max_qpm_memory_size: int = 1000,
        base_directory: str = None,
        strategy_config_path: Optional[str] = None,
        disable_metacognition: bool = False,  # YAA: Mantido, vem do CLI
        config: Optional[Dict[str, Any]] = None,
        kraken_api_key: Optional[str] = None,
        kraken_secret_key: Optional[str] = None,
        kucoin_api_key: Optional[str] = None,
        kucoin_secret_key: Optional[str] = None,
        kucoin_passphrase: Optional[str] = None,
        risk_per_trade_pct: Optional[float] = None,
        qast_historical_data_path: Optional[str] = None,
        trader_id: Optional[str] = None,
        trading_fee_pct: float = 0.0026,
        poll_interval_override: Optional[float] = None,
        position_monitor_interval: float = 5.0,
        market_data_timeout: float = DEFAULT_MARKET_DATA_TIMEOUT,
        position_monitor_timeout: float = 30.0,
        ohlcv_fetch_timeout: float = 60.0,
        ohlcv_fetch_timeout_map: Optional[Dict[tuple[str, str], float]] = None,
        ticker_fetch_timeout: float = 30.0,
        fetch_ticker_enabled: bool = True,
        adaptive_poll: bool = True,
        api_fail_threshold: Optional[int] = None,
        api_recovery_timeout: Optional[float] = None,
        max_idle_cycles: int = 60,
        stop_on_idle: bool = False,
        statsd_client: Optional[DogStatsd] = None,
        enable_hud: bool = True,
        use_webgpu: bool = False,
        qualia_universe: Optional[QUALIAQuantumUniverse] = None,
        qpm_memory: Optional[QuantumPatternMemory] = None,
        analysis_core: Optional[QualiaAnalysisCore] = None,
        simulation_core: Optional[SimulationQASTCore] = None,  # Adicionado
    ):
        """
        Inicializa o trader em tempo real do QUALIA.

        Args:
            symbols: Lista de símbolos para monitorar.
            timeframes: Lista de timeframes para analisar.
            capital: Capital inicial para trading.
            risk_profile: Perfil de risco ('conservative', 'moderate', 'aggressive').
            mode: Modo de operação ('paper_trading' ou 'live').
            data_source: Fonte de dados do mercado ('kraken' ou 'kucoin').
            duration_seconds: Duração máxima em segundos (None para executar indefinidamente).
            max_qpm_memory_size: Tamanho máximo para a memória QPM.
            base_directory: Diretório base para arquivos.
            strategy_config_path: Caminho para arquivo de configuração de estratégia.
            disable_metacognition: Se True, desabilita metacognição.
            config: Configurações adicionais.
            kraken_api_key: Chave API da Kraken.
            kraken_secret_key: Chave secreta da API da Kraken.
            kucoin_api_key: Chave API da Kucoin.
            kucoin_secret_key: Chave secreta da Kucoin.
            kucoin_passphrase: Passphrase da Kucoin.
            risk_per_trade_pct: Porcentagem de risco por trade.
            qast_historical_data_path: Caminho para dados históricos do QAST.
            trader_id: Identificador único do trader.
            trading_fee_pct: Percentual de taxa cobrada pela exchange por trade.
                Pode ser sobrescrito pela variável de ambiente ``TRADING_FEE_PCT``.
            poll_interval_override: Intervalo fixo em segundos para polling.
                Se ``None``, o intervalo será calculado automaticamente com base
                nos timeframes fornecidos.
            position_monitor_interval: Intervalo (segundos) para verificar SL/TP em
                segundo plano.
            market_data_timeout: Tempo máximo (segundos) permitido para a
                atualização de dados de mercado.
            position_monitor_timeout: Tempo máximo (segundos) permitido para o
                loop de monitoramento de posições.
            ohlcv_fetch_timeout: Tempo máximo (segundos) para aguardar resposta da
                exchange ao buscar OHLCV. Valor padrão ``60`` segundos
                (recomendado: ``90``) ou valor lido do ambiente
                ``OHLCV_FETCH_TIMEOUT``.
            ohlcv_fetch_timeout_map: Mapeamento opcional ``(símbolo, timeframe)``
                -> timeout em segundos para sobrescrever ``ohlcv_fetch_timeout``
                em casos específicos.
            ticker_fetch_timeout: Tempo máximo (segundos) para aguardar resposta da
                exchange ao buscar dados de ticker.
            fetch_ticker_enabled: Se ``False``, ignora ``fetch_ticker`` e usa o
                último valor de fechamento do candle mais recente.
            adaptive_poll: Ativa ajuste adaptativo do intervalo de polling com
                back-off exponencial.
            api_fail_threshold: Número de falhas consecutivas antes de abrir o
                circuit breaker da exchange.
            api_recovery_timeout: Tempo em segundos para tentar novamente após o
                circuito ser aberto.
            max_idle_cycles: Número máximo de ciclos sem trades antes de emitir
                um aviso.
            stop_on_idle: Se ``True``, encerra o loop principal quando
                ``max_idle_cycles`` é atingido.
            statsd_client: Cliente opcional do DogStatsd para envio de métricas.
            enable_hud: Ativa a visualização holográfica do sistema.
            use_webgpu: Habilita renderização WebGPU no frontend, se suportada.
            qualia_universe: Instância compartilhada de ``QUALIAQuantumUniverse``.
            qpm_memory: Instância compartilhada de ``QuantumPatternMemory``.
            analysis_core: Instância compartilhada de ``QUALIAConsciousness``.
            simulation_core: Instância compartilhada de ``SimulationQASTCore``.
        """
        if trading_fee_pct is None:
            logger.warning("trading_fee_pct não fornecido. Usando valor padrão 0.0026")
            trading_fee_pct = 0.0026
        else:
            try:
                trading_fee_pct = float(trading_fee_pct)
            except (TypeError, ValueError):
                logger.warning(
                    "Valor inválido para trading_fee_pct '%s'. Usando 0.0026.",
                    trading_fee_pct,
                )
                trading_fee_pct = 0.0026

        # === Inicialização de atributos básicos ===
        self.system_health = SystemHealthStatus()
        self.logger = get_logger(__name__)

        self.logger.info(
            f"Inicializando QUALIARealTimeTrader para símbolos: {symbols}, timeframes: {timeframes}"
        )

        # Identificação e configuração básica
        exchange_id = data_source.lower() if data_source else "kraken"

        # Os símbolos são normalizados apenas após a conexão com a exchange
        self.symbols_raw = list(symbols)
        self.symbols = list(symbols)
        self.symbols_normalized = False

        # Força 30s como timeout mínimo de ticker para a Kraken e Kucoin
        if exchange_id in {"kraken", "kucoin"} and (
            not ticker_fetch_timeout or ticker_fetch_timeout < 30
        ):
            ticker_fetch_timeout = 30
            self.logger.info(
                "[YAA] Timeout de ticker para %s ajustado para 30s (latency mitigation)",
                exchange_id.capitalize(),
            )
        self.ticker_fetch_timeout = ticker_fetch_timeout
        self.timeframes = timeframes
        self.capital = capital
        self.initial_capital = capital  # Preservar valor original
        self.enable_hud = enable_hud
        self.use_webgpu = use_webgpu

        # Sobreposição final por variável de ambiente
        fee_env = get_env("TRADING_FEE_PCT", None, warn=False)
        if fee_env is not None:
            try:
                trading_fee_pct = float(fee_env)
            except ValueError:
                logger.warning(
                    "Valor inválido para TRADING_FEE_PCT '%s'. Usando %s.",
                    fee_env,
                    trading_fee_pct,
                )
        self.trading_fee_pct = trading_fee_pct

        # Configurações adicionais fornecidas pelo construtor
        self.config = config if config is not None else {}
        # Instancia o TradingQASTCore com as configurações atuais
        self.qast_core = TradingQASTCore(
            self.config,
            metrics_client=statsd_client
            or (DogStatsd() if market_metrics_enabled else None),
        )

        self.fetch_ticker_enabled = fetch_ticker_enabled
        self.adaptive_poll = adaptive_poll
        self.max_idle_cycles = max_idle_cycles
        self.stop_on_idle = stop_on_idle
        if statsd_client is not None or market_metrics_enabled:
            self.statsd: Optional[DogStatsd] = statsd_client or DogStatsd()
        else:
            self.statsd = None
        self._idle_warning_emitted = False
        self._cycles_without_trades = 0
        self._last_trade_count = 0
        self.api_fail_threshold = (
            api_fail_threshold
            if api_fail_threshold is not None
            else self.config.get("api_fail_threshold")
        )
        self.api_recovery_timeout = (
            api_recovery_timeout
            if api_recovery_timeout is not None
            else self.config.get("api_recovery_timeout")
        )

        if poll_interval_override is None:
            poll_interval_override = self._determine_poll_interval()

        self.poll_interval_override = poll_interval_override
        self.default_poll_interval = poll_interval_override
        self.current_poll_interval = poll_interval_override
        self._poll_backoff_level = 0

        self.position_monitor_interval = position_monitor_interval

        mdt_env = get_env("MARKET_DATA_TIMEOUT", None, warn=False)
        if mdt_env is not None:
            try:
                self.market_data_timeout = float(mdt_env)
            except ValueError:
                logger.warning(
                    "Valor inválido para MARKET_DATA_TIMEOUT '%s'. Usando %s.",
                    mdt_env,
                    market_data_timeout,
                )
                self.market_data_timeout = float(market_data_timeout)
        else:
            self.market_data_timeout = float(
                self.config.get("market_data_timeout", market_data_timeout)
            )

        pmt_env = get_env("POSITION_MONITOR_TIMEOUT", None, warn=False)
        if pmt_env is not None:
            try:
                self.position_monitor_timeout = float(pmt_env)
            except ValueError:
                logger.warning(
                    "Valor inválido para POSITION_MONITOR_TIMEOUT '%s'. Usando %s.",
                    pmt_env,
                    position_monitor_timeout,
                )
                self.position_monitor_timeout = float(position_monitor_timeout)
        else:
            self.position_monitor_timeout = float(position_monitor_timeout)

        oft_env = get_env("OHLCV_FETCH_TIMEOUT", None, warn=False)
        if oft_env is not None:
            try:
                self.ohlcv_fetch_timeout = float(oft_env)
            except ValueError:
                logger.warning(
                    "Valor inválido para OHLCV_FETCH_TIMEOUT '%s'. Usando %s.",
                    oft_env,
                    ohlcv_fetch_timeout,
                )
                self.ohlcv_fetch_timeout = float(ohlcv_fetch_timeout)
        else:
            self.ohlcv_fetch_timeout = float(ohlcv_fetch_timeout)
        self.ohlcv_fetch_timeout_map = ohlcv_fetch_timeout_map or {}

        # Parâmetros configuráveis via ``config_loader`` / ``Settings``
        self.METACOG_DIRECTIVE_TTL_SECONDS = settings.metacog_directive_ttl_seconds
        self.REDUCE_EXPOSURE_MIN_CONFIDENCE = settings.reduce_exposure_min_confidence
        self.REDUCE_EXPOSURE_MIN_POSITION_AGE_MINUTES = (
            settings.reduce_exposure_min_position_age_minutes
        )
        self.REDUCE_EXPOSURE_CLOSE_SINGLE_POSITION = (
            settings.reduce_exposure_close_single_position
        )
        self.REDUCE_EXPOSURE_DEFAULT_PCT = settings.reduce_exposure_default_pct
        self.ORDER_EXEC_TIMEOUT = settings.order_exec_timeout
        self.SLEEP_TIME_WARNING_THRESHOLD = settings.sleep_time_warning_threshold
        self.TICKER_RETRY_ATTEMPTS = settings.ticker_retry_attempts
        self.TICKER_RECONNECT_ATTEMPTS = settings.ticker_reconnect_attempts
        self.CONNECTION_RETRY_WAIT = settings.connection_retry_wait
        self.CONNECTION_FAILURE_LIMIT = settings.connection_failure_limit
        self.CONNECTION_FAILURE_COOLDOWN = settings.connection_failure_cooldown

        # Mapear risk_profile para valores internos (suporta Português e Inglês)
        risk_profile_map = {
            "conservative": "conservative",
            "conservador": "conservative",
            "moderate": "moderate",
            "moderado": "moderate",
            "aggressive": "aggressive",
            "agressivo": "aggressive",
            "custom": "custom",
            "personalizado": "custom",
        }
        self.risk_profile = risk_profile_map.get(
            risk_profile.lower(), risk_profile.lower()
        )
        # Mapear mode para valores internos
        mode_map = {
            "simulation": "simulation",
            "simulação": "simulation",
            "simulacao": "simulation",
            "live": "live",
            "ao_vivo": "live",
            "paper_trading": "paper_trading",
            "teste_papel": "paper_trading",
        }
        self.mode = mode_map.get(mode.lower(), mode.lower())
        if data_source.lower() not in ["kraken", "kucoin", "mock"]:
            raise ValueError("data_source deve ser 'kraken', 'kucoin' ou 'mock'")
        self.data_source = data_source.lower()
        self.initial_mode = mode  # Preservar valor original para logs se necessário
        self.initial_data_source = data_source
        self.duration_seconds = duration_seconds

        # Configuração de arquivos e diretórios
        self._setup_paths(
            strategy_config_path,
            base_directory,
            qast_historical_data_path,
        )

        # YAA: CORREÇÃO P-7 - Cache para strategy parameters
        self._strategy_config_cache = None
        self._strategy_config_last_modified = None

        # Controles de execução
        self.disable_metacognition = (
            disable_metacognition  # YAA: Mantido para compatibilidade
        )
        self.is_running = False
        self.start_time = datetime.now(timezone.utc)

        # --- YAA: Segurança de credenciais ---
        self._configure_exchange(
            kraken_api_key,
            kraken_secret_key,
            kucoin_api_key,
            kucoin_secret_key,
            kucoin_passphrase,
        )

        # --- YAA: Estado de monitoramento ---
        self._last_health_check = time.time()
        self._consecutive_errors = 0
        self._last_error = None
        self._health_monitor_task = None  # RESTAURADA
        self._ticker_error_count = 0
        self._connection_failure_count = 0
        self._previous_cycle_exception = False
        self._cycle_failure_messages: list[str] = []

        # --- YAA: Lock para posições e rastreamento de order_ids ---
        self._pos_lock = asyncio.Lock()  # EXISTENTE
        self.live_order_ids = set()  # EXISTENTE
        self.last_trade_close_time = {}  # Track last close time for each symbol

        self.trader_id = (
            trader_id or f"qualia_trader_{uuid.uuid4().hex[:8]}"
        )  # EXISTENTE
        self.logger.info(
            f"QUALIARealTimeTrader instanciado com ID: {self.trader_id}"
        )  # EXISTENTE

        # --- Inicialização original removida ---
        # Quaisquer passos adicionais de preparação devem ocorrer
        # após a configuração básica de monitoramento e segurança.

        self.symbols = symbols
        self.timeframes = timeframes
        if not self.timeframes:
            raise ValueError("A lista de timeframes não pode ser vazia.")
        self.primary_timeframe = timeframes[0]
        self.capital = capital
        self.risk_profile = risk_profile
        # Mantém valores já normalizados de mode e data_source
        self.duration_seconds = duration_seconds
        self.start_time = datetime.now(
            timezone.utc
        )  # Definido aqui, usado no _main_loop
        self.loaded_strategy_configs: Dict[str, Any] = {}
        # YAA: Para armazenar configs de perfil de risco
        self.risk_profile_configs: Dict[str, Any] = {}
        # Increase default failure limit to avoid premature pauses on flaky
        # connections. The first pause now occurs after five consecutive
        # failures unless overridden via configuration.
        self.ticker_failure_limit = self.config.get("ticker_failure_limit", 5)
        self.ticker_reconnect_attempts = self.config.get(
            "ticker_reconnect_attempts",
            self.TICKER_RECONNECT_ATTEMPTS,
        )
        self.connection_retry_wait = self.config.get(
            "connection_retry_wait",
            self.CONNECTION_RETRY_WAIT,
        )
        self.connection_failure_limit = self.config.get(
            "connection_failure_limit",
            self.CONNECTION_FAILURE_LIMIT,
        )
        self.connection_failure_cooldown = self.config.get(
            "connection_failure_cooldown",
            self.CONNECTION_FAILURE_COOLDOWN,
        )
        self.verbose_ticker_logging = self.config.get("verbose_ticker_logging", False)
        self._consecutive_ticker_failures = 0
        self._ticker_failure_notified = False
        self.system_health.ticker_fetch = "ok"
        self.system_health.status = "ok"

        # Back-off e monitoramento de posições
        self.position_monitor_max_interval = self.config.get(
            "position_monitor_max_interval",
            self.position_monitor_interval * 8,
        )
        self._current_monitor_interval = self.position_monitor_interval
        self._monitor_backoff_level = 0

        # YAA: Armazenar o percentual de risco customizado
        self.risk_per_trade_pct = risk_per_trade_pct

        # YAA: Tentar obter max_position_capital_pct diretamente do self.config primeiro
        self.max_position_capital_pct_custom = self.config.get(
            "max_position_capital_pct"
        )

        if self.max_position_capital_pct_custom is not None:
            self.logger.debug(
                f"QUALIARealTimeTrader __init__: Max Position Capital %% (do config/CLI direto): {self.max_position_capital_pct_custom}"
            )
        else:
            # Fallback para a lógica anterior se não encontrado diretamente no config
            self.logger.debug(
                "QUALIARealTimeTrader __init__: Max Position Capital %% (do config/CLI direto) não fornecido. Tentando via risk_profile_settings."
            )
            self.max_position_capital_pct_custom = (
                self.config.get("risk_profile_settings", {})
                .get(self.risk_profile, {})
                .get(self.symbols[0] if self.symbols else "default", {})
                .get("max_position_capital_percentage")
            )
            if self.max_position_capital_pct_custom is not None:
                self.logger.debug(
                    f"QUALIARealTimeTrader __init__: Max Position Capital %% (via risk_profile_settings): {self.max_position_capital_pct_custom}"
                )
            else:
                self.logger.debug(
                    "QUALIARealTimeTrader __init__: Max Position Capital %% (via risk_profile_settings): Não fornecido."
                )

        self.logger.debug(
            f"QUALIARealTimeTrader __init__: Risk Profile: {self.risk_profile}"
        )
        self.logger.debug(
            f"QUALIARealTimeTrader __init__: Risk per Trade %% (do argumento): {self.risk_per_trade_pct}"
        )
        self.logger.debug(
            f"QUALIARealTimeTrader __init__: Strategy Config Path: {self.strategy_config_path}"
        )

        # YAA: Ensure correct initialization of these attributes
        # YAA: Corrigido para QASTEvolutionaryStrategy no type hint, se possível, ou Any
        self.qast_engines: Dict[str, Any] = {}  # Anteriormente QASTEvolutionaryStrategy
        self.strategies_additional_tf: Dict[
            str, Dict[str, Any]  # Anteriormente QUALIAEnhancedScalpingStrategy
        ] = {
            s: {} for s in self.symbols
        }  # Use self.symbols here

        # YAA: Para dados históricos dedicados ao QAST
        self.qast_historical_data_path = (
            setup_utils._normalize_path(qast_historical_data_path)
            if qast_historical_data_path
            else self.qast_historical_data_path
        )
        self.qast_historical_data_store: Dict[str, Dict[str, pd.DataFrame]] = {}

        self.market_data: Dict[str, Dict[str, pd.DataFrame]] = {s: {} for s in symbols}
        self.strategies: Dict[str, Any] = (
            {}
        )  # Anteriormente QUALIAEnhancedScalpingStrategy

        # YAA: Atributos para rastreamento de timestamps OHLCV
        self.last_ohlcv_timestamp: Dict[str, Dict[str, int]] = {}
        self.last_ohlcv_new_rows: Dict[str, Dict[str, int]] = {}

        # YAA: Instâncias dos componentes principais de QUALIA
        # Podem ser injetadas externamente via ``setup_core_components``
        self.qualia_universe: Optional[QUALIAQuantumUniverse] = qualia_universe
        self.qmc: Optional[QuantumMetricsCalculator] = None
        self.qpm_memory: Optional[QuantumPatternMemory] = qpm_memory
        self.adaptive_consciousness_engine: Optional[AdaptiveConsciousnessEvolution] = (
            None
        )
        # YAA: Consciência Executiva
        self.metacognition_executive: Optional[QUALIAMetacognitionTrading] = None
        # YAA: Módulo de análise interna da consciência (PCA/KMeans)
        self.qualia_analysis_core: Optional[QualiaAnalysisCore] = analysis_core
        self.simulation_core: Optional[SimulationQASTCore] = simulation_core
        # YAA: Para armazenar o último contexto metacognitivo
        self._last_metacognitive_ctx: Optional[MetacognitiveContext] = None
        # YAA: D-03.2 - Live Feed Integration
        self.live_feed_integration: Optional[LiveFeedIntegration] = None

        self.wallet_state: Dict[str, Any] = {
            "initial_capital": capital,
            "current_capital": capital,
            "available_cash": capital,
            "positions_value": 0.0,
            "total_pnl": 0.0,
            "total_pnl_pct": 0.0,
            "current_drawdown": 0.0,
            "max_drawdown": 0.0,
            "win_rate": 0.0,
            "loss_rate": 0.0,
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "total_fees_paid": 0.0,
        }
        # Permite múltiplas posições por símbolo
        self.open_positions: Dict[str, List[OpenPosition]] = {}
        # Timestamp do último fechamento de trade por símbolo
        self.last_trade_close_time: Dict[str, float] = defaultdict(float)
        self.trade_history: List[Dict[str, Any]] = []
        self.daily_pnl: Dict[str, float] = {}

        # Configurações de confiança e contadores de sinais
        metacog_conf = self.config.get("metacognition_config", {})
        self.trade_decision_confidence_threshold = metacog_conf.get(
            "trade_decision_confidence_threshold", 0.6
        )
        self.signal_counters = {
            "processed": 0,
            "rejected_risk": 0,
            "rejected_confidence": 0,
        }

        # YAA: Cache para DataFrames com indicadores calculados
        # A chave será (symbol, timeframe, last_candle_timestamp)
        self.indicator_df_cache: Dict[Tuple[str, str, pd.Timestamp], pd.DataFrame] = {}

        # Rastrea o timestamp do último candle efetivamente adicionado ao
        # histórico para cada par e timeframe. Utilizado para calcular o
        # parâmetro ``since`` nas próximas consultas de OHLCV e evitar a
        # repetição de candles já conhecidos.
        self.last_ohlcv_timestamp: Dict[str, Dict[str, int]] = defaultdict(dict)

        # Quantidade de novas linhas obtidas no último fetch OHLCV por par e
        # timeframe. Isso permite ajustar o parâmetro ``since`` em chamadas
        # subsequentes evitando repetição de candles quando a exchange retorna
        # apenas dados duplicados.
        self.last_ohlcv_new_rows: Dict[str, Dict[str, int]] = defaultdict(dict)

        # Rastreamento de avisos de volume ausente por ciclo
        self._volume_warning_logged: Dict[Tuple[str, str], bool] = {}

        self.trading_active = False
        self.shutdown_event = asyncio.Event()
        # Flag to ensure performance report is generated only once
        self._final_report_emitted = False
        # Evita múltiplas execuções de ``handle_critical_failure``
        self._critical_failure_handled = False
        self._position_monitor_task: Optional[asyncio.Task] = None
        self._qast_schedule_task: Optional[asyncio.Task] = None
        qsched_env = get_env("QAST_SCHEDULE_INTERVAL_MINUTES", None, warn=False)
        if qsched_env is not None:
            try:
                self.qast_schedule_interval_minutes = int(qsched_env)
            except ValueError:
                logger.warning(
                    "Valor inválido para QAST_SCHEDULE_INTERVAL_MINUTES '%s'. Usando 30.",
                    qsched_env,
                )
                self.qast_schedule_interval_minutes = 30
        else:
            self.qast_schedule_interval_minutes = 30

        # YAA: Adicionar para armazenar dados de ticker e sinalizar quando
        # dados provenientes de OHLCV foram usados como fallback.
        self.current_tickers: Dict[str, Dict[str, Any]] = {s: {} for s in self.symbols}
        self.stale_tickers: Dict[str, bool] = {s: False for s in self.symbols}
        self._pending_ticker_tasks: Dict[str, asyncio.Task] = {}

        self.exchange: Optional[CryptoDataFetcher] = None

        # Flag utilizada para evitar tentativas de reconexão enquanto o
        # desligamento está em andamento. Isso previne que tarefas em segundo
        # plano recriem a conexão após ``close_exchange`` ser chamado.
        self._prevent_reconnect: bool = False

        # A instância da exchange é criada em ``_init_system_components``.
        # Isso evita duplicação de lógica e garante que a conexão seja
        # configurada apenas quando ``initialize`` é chamado.

        # A lógica de inicialização da conexão (chamar self.exchange.initialize_connection())
        # permanece em _initialize_exchange_connection, que é chamada no início do _main_loop
        # para modos 'live' e 'paper_trading'. Isso está correto.

        # YAA: Inicializar QUALIAQuantumUniverse com os parâmetros corretos.
        # Removido n_steps, adicionados scr_depth, base_lambda, alpha,
        # retro_strength, num_ctc_qubits.
        # REMOVIDA A INSTANCIAÇÃO ANTECIPADA DE self.qualia_universe
        # self.qualia_universe = QUALIAQuantumUniverse(
        #     n_qubits=5,
        #     scr_depth=3,
        #     base_lambda=0.52359877559,  # Aproximadamente np.pi/6
        #     alpha=0.1,
        #     retro_strength=0.0,
        #     num_ctc_qubits=1,
        # )  # Exemplo, idealmente configurável

        # YAA: Instanciar QuantumMetricsCalculator APÓS o universo (para
        # n_qubits) e ANTES da metacognição/QPM
        # REMOVIDA A INSTANCIAÇÃO ANTECIPADA DE self.quantum_metrics_calculator
        # qmc_config = {
        #     "trading_symbols": self.symbols,
        #     "trading_primary_timeframe": self.primary_timeframe,
        # }
        # self.quantum_metrics_calculator = QuantumMetricsCalculator(  # << Este era self.quantum_metrics_calculator, não self.qmc
        #     n_qubits=self.qualia_universe.n_qubits if self.qualia_universe else 5,  # Garante consistência, fallback para 5 se universo não inicializado
        #     config=qmc_config,
        # )
        # self.logger.info(
        #     f"QuantumMetricsCalculator inicializado e configurado para {self.symbols} no timeframe {self.primary_timeframe}."
        # )

        # YAA: Instanciar QuantumPatternMemory PRIMEIRO
        # REMOVIDA A INSTANCIAÇÃO DE self.qpm_memory DAQUI
        # self.qpm_persistence_path = os.path.join(
        #     self.base_directory, "data", "cache", "qpm_memory.json"
        # )  # YAA: Path definido
        # self.qpm_memory = QuantumPatternMemory(
        #     max_memory_size=max_qpm_memory_size,
        #     similarity_threshold=0.6,  # Alterado de 0.85 para 0.6
        #     persistence_path=self.qpm_persistence_path,  # YAA: Passando o path
        # )
        # self.logger.info(
        #     f"QuantumPatternMemory inicializada. Path de persistência: {self.qpm_persistence_path}"
        # )

        # YAA: Adicionar atributo para armazenar o contexto metacognitivo mais
        self.current_metacognitive_context: Optional[MetacognitiveContext] = None

        # YAA: Mover a definição de disable_metacognition para antes de _init_system_components
        # self.disable_metacognition = self.config.get('disable_metacognition',
        # False) # YAA: REMOVIDO - self.disable_metacognition vem do construtor
        # Atribuir o parâmetro do construtor
        self.disable_metacognition = disable_metacognition

        # Carregar configurações necessárias
        self._load_configs()

        # YAA: Extrair log_level da configuração carregada e armazená-lo em self.config
        log_level_from_config = None
        if (
            self.loaded_strategy_configs
            and "strategy_config" in self.loaded_strategy_configs
        ):
            log_level_from_config = self.loaded_strategy_configs["strategy_config"].get(
                "log_level"
            )

        if log_level_from_config:
            # Armazenar no self.config do trader para ser acessado em main()
            self.config["log_level_from_config"] = log_level_from_config.upper()
            # Log inicial pode usar o logger default antes da reconfiguração final em main()
            # self.logger.info(f"Nível de log identificado na configuração JSON: {self.config['log_level_from_config']}")
        else:
            self.config["log_level_from_config"] = None
            # self.logger.info("Nenhum nível de log especificado na configuração JSON.")

        # Extrair parâmetro de validade do cache OHLCV
        default_cache_age = 60
        if (
            self.loaded_strategy_configs
            and "strategy_config" in self.loaded_strategy_configs
        ):
            default_cache_age = self.loaded_strategy_configs["strategy_config"].get(
                "max_cache_age_minutes",
                default_cache_age,
            )
        self.max_cache_age_minutes = self.config.get(
            "max_cache_age_minutes",
            default_cache_age,
        )

        default_monitor_threshold = 0.005
        if self.loaded_strategy_configs:
            default_monitor_threshold = self.loaded_strategy_configs.get(
                "monitor_close_threshold",
                default_monitor_threshold,
            )
        self.monitor_close_threshold = self.config.get(
            "monitor_close_threshold",
            default_monitor_threshold,
        )

        # A inicialização dos componentes do sistema é adiada para o método
        # ``initialize``. Isso evita executar rotinas assíncronas dentro do
        # construtor e permite maior flexibilidade em cenários de teste.

        self.result_writer = ResultWriter(base_dir=self.base_directory)

        # YAA: Carregar dados históricos para QAST, se o caminho for fornecido
        if self.qast_historical_data_path:
            qast_path = setup_utils._normalize_path(self.qast_historical_data_path)
            self.logger.info(
                f"Tentando carregar dados históricos para QAST de: {qast_path}"
            )
            try:
                df_qast_hist = pd.read_csv(qast_path)
                self.logger.debug(
                    f"[QAST_CSV_LOAD] CSV {qast_path} carregado. {len(df_qast_hist)} linhas iniciais."
                )
                if not df_qast_hist.empty:
                    self.logger.debug(
                        f"[QAST_CSV_LOAD] Primeiras 5 linhas ANTES do processamento de timestamp:\\n{df_qast_hist.head().to_string()}"
                    )

                required_cols = [
                    "timestamp",
                    "open",
                    "high",
                    "low",
                    "close",
                    "volume",
                ]
                if not all(col in df_qast_hist.columns for col in required_cols):
                    self.logger.error(
                        f"Arquivo CSV de dados QAST ({qast_path}) não contém todas as colunas requeridas: {required_cols}. Colunas encontradas: {df_qast_hist.columns.tolist()}"
                    )
                else:
                    self.logger.debug(
                        f"[QAST_CSV_LOAD] Coluna 'timestamp' ANTES de pd.to_datetime (primeiros 5 valores):\\n{df_qast_hist['timestamp'].head().to_string()}"
                    )
                    # Tentar detectar o formato do timestamp se for uma string
                    # Exemplo: se for '2023-01-01 10:00:00' -> format='%Y-%m-%d %H:%M:%S'
                    # Exemplo: se for Unix timestamp em segundos -> unit='s'
                    # YAA: Corrigido para especificar unit='ms' pois os logs
                    # indicam timestamps em milissegundos.
                    df_qast_hist["timestamp_original_dtype"] = df_qast_hist[
                        "timestamp"
                    ].dtype
                    df_qast_hist["timestamp"] = pd.to_datetime(
                        df_qast_hist["timestamp"],
                        unit="ms",
                        errors="coerce",
                        utc=True,
                    )

                    self.logger.debug(
                        f"[QAST_CSV_LOAD] Coluna 'timestamp' DEPOIS de pd.to_datetime (primeiros 5 valores):\\n{df_qast_hist['timestamp'].head().to_string()}"
                    )
                    self.logger.debug(
                        f"[QAST_CSV_LOAD] Dtype original da coluna 'timestamp': {df_qast_hist['timestamp_original_dtype'].iloc[0] if not df_qast_hist.empty else 'N/A'}"
                    )
                    self.logger.debug(
                        f"[QAST_CSV_LOAD] Dtype da coluna 'timestamp' após to_datetime: {df_qast_hist['timestamp'].dtype}"
                    )

                    num_nats_after_coerce = df_qast_hist["timestamp"].isnull().sum()
                    self.logger.debug(
                        f"[QAST_CSV_LOAD] Número de NaTs na coluna 'timestamp' após 'coerce': {num_nats_after_coerce} de {len(df_qast_hist)}"
                    )

                    df_qast_hist.dropna(subset=["timestamp"], inplace=True)
                    self.logger.debug(
                        f"[QAST_CSV_LOAD] Número de linhas após dropna em 'timestamp': {len(df_qast_hist)}"
                    )

                    if not df_qast_hist.empty:
                        df_qast_hist.set_index("timestamp", inplace=True)
                        df_qast_hist.sort_index(inplace=True)
                        self.logger.debug(
                            f"[QAST_CSV_LOAD] DataFrame com índice 'timestamp'. Tipo do índice: {df_qast_hist.index.dtype}"
                        )
                        self.logger.debug(
                            f"[QAST_CSV_LOAD] Índice contém NaNs: {df_qast_hist.index.hasnans}"
                        )
                        self.logger.debug(
                            f"[QAST_CSV_LOAD] Primeiras 5 linhas DO DATAFRAME INDEXADO FINAL:\\n{df_qast_hist.head().to_string()}"
                        )
                        self.logger.debug(
                            f"[QAST_CSV_LOAD] Últimas 5 linhas DO DATAFRAME INDEXADO FINAL:\\n{df_qast_hist.tail().to_string()}"
                        )

                        if (
                            not isinstance(df_qast_hist.index, pd.DatetimeIndex)
                            or df_qast_hist.index.hasnans
                        ):
                            self.logger.error(
                                f"Falha ao criar um DatetimeIndex válido e limpo a partir da coluna 'timestamp' do arquivo QAST CSV ({qast_path})."
                            )
                        else:
                            for symbol_qast in self.symbols:
                                if symbol_qast not in self.qast_historical_data_store:
                                    self.qast_historical_data_store[symbol_qast] = {}
                                self.qast_historical_data_store[symbol_qast][
                                    self.primary_timeframe
                                ] = df_qast_hist.copy()
                                self.logger.info(
                                    f"Dados históricos para QAST ({symbol_qast}@{self.primary_timeframe}) carregados e processados de {qast_path}: {len(df_qast_hist)} candles. Timestamp do primeiro candle: {df_qast_hist.index.min()}, último: {df_qast_hist.index.max()}"
                                )
                    else:
                        self.logger.error(
                            f"[QAST_CSV_LOAD] DataFrame histórico para QAST ficou VAZIO após processamento de timestamps para {qast_path}."
                        )

            except FileNotFoundError:
                self.logger.error(
                    f"Arquivo de dados históricos para QAST não encontrado: {qast_path}"
                )
            except (OSError, ValueError, pd.errors.ParserError) as exc:
                self.logger.exception(
                    "Erro ao carregar ou processar dados históricos para QAST de %s: %s",
                    qast_path,
                    exc,
                )
        else:
            self.logger.info(
                "Nenhum caminho para dados históricos QAST fornecido. QAST usará dados de self.market_data se evoluir."
            )
        # --- Fim da lógica de carregamento de dados QAST ---

        # --- YAA: Inicialização de segurança e monitoramento ---
        if (
            self._secure_config
        ):  # Verifica se as credenciais foram realmente fornecidas e criptografadas
            self.logger.debug("Segurança e monitoramento configurados com sucesso.")
        else:
            self.logger.debug(
                "Segurança e monitoramento não configurados. Executando em modo 'paper_trading' sem credenciais."
            )

        self.trader_id = trader_id if trader_id else str(uuid.uuid4())
        self.current_timestamp = (
            None  # YAA: Adicionado para rastrear o tempo atual do backtest
        )
        self.backtest_results: Dict[str, Any] = {}
        self.message_broker = None  # YAA: Adicionado para sistema de mensagens
        self.cycle_count = 0  # YAA: Inicializar cycle_count
        self.current_cycle_trace_id: Optional[str] = None

        # self._load_configs() is executed earlier in __init__ before component
        # initialization. Calling it again here would be redundant and could
        # lead to unnecessary file reads, so the invocation has been removed to
        # keep initialization deterministic.

        # Inicializações específicas de QUALIARealTimeTrader
        self.symbols = symbols
        self.timeframes = timeframes
        self.risk_profile = risk_profile
        self.initial_capital = capital  # YAA: Corrigido de initial_capital para capital
        self.shared_context_for_strategies = {}
        self.strategy_instances: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.qast_engines: Dict[str, Dict[str, Optional[QASTEvolutionaryStrategy]]] = (
            defaultdict(dict)
        )
        # self.risk_managers: Dict[str, QUALIARiskManager] = {} # YAA: REMOVIDA ESTA LINHA PROBLEMÁTICA
        self.market_data: Dict[str, Dict[str, pd.DataFrame]] = defaultdict(
            dict
        )  # {symbol: {timeframe: df}}
        self.last_data_update_time: Dict[str, Dict[str, Optional[datetime]]] = (
            defaultdict(lambda: defaultdict(lambda: None))
        )
        self.active_positions: Dict[str, Optional[OpenPosition]] = defaultdict(
            lambda: None
        )
        self.trade_history: List[Dict] = []
        self.daily_pnl: Dict[str, float] = defaultdict(float)
        self.last_reset_day: Optional[int] = None
        self.backtest_results: Optional[Dict] = None
        self.is_backtesting_active = False
        self.backtest_data_iterator: Optional[Dict[str, Dict[str, Iterator]]] = None
        self.backtest_current_kline: Optional[Dict[str, Dict[str, pd.Series]]] = None
        self._current_quantum_signature_packets: Dict[str, QuantumSignaturePacket] = (
            {}
        )  # YAA: Inicialização adicionada

        self.shutdown_event = asyncio.Event()
        self.main_loop_task: Optional[asyncio.Task] = None
        # Inicializar dicionários de dados
        # Pre-inicializa estruturas de mercado para permitir uso do trader
        # mesmo quando ``_init_system_components`` é pulado em testes.
        # Usa todos os símbolos fornecidos durante a criação da instância.
        self.market_data: Dict[str, Dict[str, pd.DataFrame]] = {
            s: {} for s in self.symbols
        }
        self.current_tickers: Dict[str, Dict[str, float]] = {
            s: {} for s in self.symbols
        }
        self.position_meta = {}  # type: Dict[str, Dict[str, Any]]
        # Agora suporta múltiplas posições por símbolo
        self.open_positions = {}  # type: Dict[str, List[OpenPosition]]
        self.trade_history = []  # type: List[Dict[str, Any]]

        # Estruturas serão preenchidas após a normalização dos símbolos

        # Tentar carregar posições abertas de arquivo
        self._load_open_positions()

        self.open_positions = {}
        self.wallet = {"USD": self.capital}

        from .config import config

        self.order_journal = OrderJournal(config.order_journal_file)

        # Inicializa dicionário de risk managers e popula com base nas
        # configurações carregadas. Isso garante que testes que utilizam
        # o trader sem chamar ``initialize`` já tenham os risk managers
        # disponíveis para todos os símbolos.
        self.risk_managers: Dict[str, QUALIARiskManagerBase] = {}
        # Risk managers serao configurados em `_init_system_components` apos a
        # normalizacao dos simbolos.
        self._risk_manager_sl_adj = {}

        # --- Visualização Dinâmica ---
        self.hud_manager = HUDManager()

        self.logger.info(
            "QUALIARealTimeTrader __init__ COMPLETED. self.symbols: %s, "
            "self.risk_managers keys: %s",
            self.symbols,
            list(self.risk_managers.keys()),
        )

        # YAA CORREÇÃO: Inicializar AdaptiveHistoryManager
        self.adaptive_history_manager = AdaptiveHistoryManager()

        # Registrar capacidades conhecidas da Kraken
        if data_source.lower() == "kraken":
            kraken_capability = ExchangeCapability(
                name="kraken",
                max_candles_per_request={
                    "1m": 720,
                    "5m": 720,
                    "15m": 720,
                    "30m": 720,
                    "1h": 720,
                    "4h": 720,
                    "1d": 720,
                },
                max_historical_candles={
                    "1m": 720,
                    "5m": 720,
                    "15m": 720,
                    "30m": 720,
                    "1h": 720,
                    "4h": 720,
                    "1d": 720,
                },
                rate_limit_ms=1000,
                supports_batch=False,
            )
            self.adaptive_history_manager.register_exchange_capability(
                kraken_capability
            )

    async def initialize(self) -> None:
        """Initialize asynchronous system components."""

        import importlib

        import qualia.config as config_module
        import qualia.config.settings as settings_module

        importlib.reload(settings_module)
        importlib.reload(config_module)

        try:
            await self._init_system_components()
            await self._fetch_dynamic_fee()
            await self.qast_core.initialize()
        except DataRequirementsError as exc:
            logger.error(str(exc))
            if self.exchange and hasattr(self.exchange, "fetch_historical_data"):
                for symbol in self.symbols:
                    try:
                        await self.exchange.fetch_historical_data(
                            MarketSpec(symbol=symbol, timeframe=self.primary_timeframe),
                            use_cache=True,
                        )
                    except (
                        ccxt.NetworkError,
                        ccxt.ExchangeError,
                        ValueError,
                    ) as fetch_exc:
                        logger.error(
                            "Erro ao baixar histórico adicional para %s: %s",
                            symbol,
                            fetch_exc,
                            exc_info=True,
                        )
                        logger.info(
                            "Utilize 'fetch_historical_data' manualmente e reinicie o sistema."
                        )
                        raise SystemExit(1) from fetch_exc

                logger.info(
                    "Histórico adicional baixado. Reinicie o sistema para prosseguir."
                )
            else:
                logger.info(
                    "Histórico insuficiente. Consulte a documentação para importar os dados."
                )
            raise SystemExit(1)

    def _get_current_price(self, symbol: str) -> Optional[float]:
        """
        Obtém o preço atual para um símbolo, priorizando o ticker e recorrendo aos dados OHLCV.
        """
        # Em modo live/papel, o ticker é a fonte mais precisa.
        if self.mode in ["live", "paper_trading"]:
            if self.current_tickers and symbol in self.current_tickers:
                ticker = self.current_tickers[symbol]
                price = get_price_from_ticker(ticker)
                if price is not None:
                    return price

        # Para simulação ou como fallback, usar o último preço de fechamento do DataFrame.
        if (
            symbol in self.market_data
            and self.primary_timeframe in self.market_data[symbol]
        ):
            df = self.market_data[symbol][self.primary_timeframe]
            if isinstance(df, pd.DataFrame) and not df.empty and "close" in df.columns:
                return df["close"].iloc[-1]

        logger.warning(f"Não foi possível obter o preço atual para {symbol}.")
        return None

    def _positions_close_to_triggers(
        self, snapshot: List[Tuple[str, List[OpenPosition]]]
    ) -> bool:
        """Return ``True`` if any position is near or beyond its SL/TP."""

        threshold = self.monitor_close_threshold
        for symbol, positions in snapshot:
            current_price = self._get_current_price(symbol)
            if current_price is None:
                continue
            for pos in positions:
                if pos.stop_loss:
                    crossed = (
                        pos.side == "buy" and current_price <= pos.stop_loss
                    ) or (pos.side == "sell" and current_price >= pos.stop_loss)
                    near = (
                        abs(current_price - pos.stop_loss) / current_price <= threshold
                    )
                    if crossed or near:
                        return True

                if pos.take_profit:
                    crossed = (
                        pos.side == "buy" and current_price >= pos.take_profit
                    ) or (pos.side == "sell" and current_price <= pos.take_profit)
                    near = (
                        abs(current_price - pos.take_profit) / current_price
                        <= threshold
                    )
                    if crossed or near:
                        return True

        return False

    def _load_strategy_parameters(self) -> None:
        """Load strategy parameters from a JSON file with caching."""
        resolved_config_path = self._resolve_strategy_config_path()
        if not resolved_config_path:
            return

        if not os.path.exists(resolved_config_path):
            logger.warning(
                "Arquivo de configuração de estratégia '%s' não encontrado. Utilizando defaults.",
                self.strategy_config_path,
            )
            self.loaded_strategy_configs = {}
            self.risk_profile_configs = {}
            return

        try:
            if self._cache_strategy_config(resolved_config_path):
                return

            with open(resolved_config_path, "r", encoding="utf-8") as f:
                all_configs = json.load(f)

            self.loaded_strategy_configs = all_configs
            self.risk_profile_configs = all_configs.get("risk_profile_settings", {})

            self._validate_preload_candles()

            self._strategy_config_cache = {
                "loaded_strategy_configs": self.loaded_strategy_configs,
                "risk_profile_configs": self.risk_profile_configs,
                "resolved_path": resolved_config_path,
            }

            logger.info("Configurações carregadas de '%s'", resolved_config_path)
            if not self.risk_profile_configs:
                logger.warning(
                    "Seção 'risk_profile_settings' ausente em '%s'",
                    resolved_config_path,
                )
        except FileNotFoundError:
            logger.error(
                "Arquivo de configuração '%s' não encontrado durante a leitura.",
                resolved_config_path,
            )
            self.loaded_strategy_configs = {}
            self.risk_profile_configs = {}
        except json.JSONDecodeError:
            logger.error(
                "JSON inválido em '%s'. Configurações padrão carregadas.",
                resolved_config_path,
            )
            self.loaded_strategy_configs = {}
            self.risk_profile_configs = _FALLBACK_RISK_PROFILE_SETTINGS.copy()
        except OSError as e:
            logger.error(
                "Erro ao ler arquivo de configuração '%s': %s",
                resolved_config_path,
                e,
            )
            self.loaded_strategy_configs = {}
            self.risk_profile_configs = {}

    def _resolve_strategy_config_path(self) -> Optional[Path]:
        """Return the resolved strategy configuration path or ``None``."""
        if not self.strategy_config_path:
            logger.info(
                "Nenhum strategy_config_path fornecido; usando configurações padrão."
            )
            self.loaded_strategy_configs = {}
            self.risk_profile_configs = {}
            return None

        cfg_path = Path(self.strategy_config_path)
        if cfg_path.is_absolute():
            return cfg_path

        if self.base_directory:
            potential_path = Path(self.base_directory) / cfg_path
            if potential_path.exists():
                logger.info(
                    "strategy_config_path '%s' resolvido para '%s'",
                    self.strategy_config_path,
                    str(potential_path),
                )
                return potential_path
        potential_path_cwd = Path.cwd() / cfg_path
        if potential_path_cwd.exists():
            return potential_path_cwd
        return None

    def _cache_strategy_config(self, path: Path) -> bool:
        """Return ``True`` if cached config is still valid for ``path``."""
        current_mtime = os.path.getmtime(path)
        cached = (
            self._strategy_config_cache is not None
            and self._strategy_config_last_modified is not None
            and current_mtime == self._strategy_config_last_modified
            and self._strategy_config_cache.get("resolved_path") == path
        )
        if cached:
            logger.debug("Usando configuração em cache para %s", path)
            self.loaded_strategy_configs = self._strategy_config_cache[
                "loaded_strategy_configs"
            ]
            self.risk_profile_configs = self._strategy_config_cache[
                "risk_profile_configs"
            ]
            return True
        self._strategy_config_last_modified = current_mtime
        return False

    def _validate_preload_candles(self) -> None:
        """Ensure required preload candles are configured."""
        try:
            strat_cfg = self.loaded_strategy_configs.get("strategy_config", {})
            alias = strat_cfg.get("name", "NovaEstrategiaQUALIA")
            params = strat_cfg.get("params", {})
            dummy_symbol = self.symbols[0] if self.symbols else "BTC/USDT"
            context = {
                "symbol": dummy_symbol,
                "timeframe": self.primary_timeframe,
                "qpm_instance": self.qpm_memory or get_qpm_instance(),
            }
            strategy = StrategyFactory().create_strategy(alias, params, context)
            required = getattr(strategy, "required_initial_data_length", 0)
            preload_key = f"preload_candles_{self.primary_timeframe}"
            configured = strat_cfg.get(preload_key)
            if configured is None or configured < required:
                logger.warning(
                    "%s ausente ou com %s candles; usando %s.",
                    preload_key,
                    configured,
                    required,
                )
                strat_cfg[preload_key] = required
        except (KeyError, AttributeError, ValueError, TypeError) as exc:  # noqa: BLE001
            logger.exception("Falha ao validar preload candles: %s", exc)

    def _setup_paths(
        self,
        strategy_config_path: Optional[str],
        base_directory: Optional[str],
        qast_historical_data_path: Optional[str],
    ) -> None:
        """Delegate path normalization to :mod:`setup_utils`."""
        setup_utils.setup_paths(
            self,
            strategy_config_path,
            base_directory,
            qast_historical_data_path,
        )

    def _configure_exchange(
        self,
        kraken_api_key: Optional[str],
        kraken_secret_key: Optional[str],
        kucoin_api_key: Optional[str],
        kucoin_secret_key: Optional[str],
        kucoin_passphrase: Optional[str],
    ) -> None:
        """Delegate exchange credential setup to :mod:`setup_utils`."""
        setup_utils.configure_exchange(
            self,
            kraken_api_key,
            kraken_secret_key,
            kucoin_api_key,
            kucoin_secret_key,
            kucoin_passphrase,
        )

    def _load_configs(self) -> None:
        """Load all configuration files used during initialization."""
        self._load_strategy_parameters()
        self._load_risk_profile_settings()

    # ------------------------------------------------------------------
    # Component Setup Helpers
    # ------------------------------------------------------------------
    def _setup_risk_managers(self) -> None:
        """Garantir risk managers para todos os símbolos, mesmo quando `qast_core` estiver presente."""
        # 1. Tentar via QAST Core (quando existente)
        if hasattr(self, "qast_core") and hasattr(self.qast_core, "_setup_risk_managers"):
            self.qast_core._setup_risk_managers()
            self.risk_managers = getattr(self.qast_core, "risk_managers", {})
        # 2. Garantir que todos os símbolos tenham instância; se faltar, usar fallback genérico
        missing = [s for s in self.symbols if s not in self.risk_managers]
        if missing:
            logger.debug(
                "RiskManager ausente via qast_core para %s; criando com fallback position_manager",
                missing,
            )
            position_manager.setup_risk_managers(self)
        logger.info("Risk managers configurados para: %s", list(self.risk_managers.keys()))

    def _setup_universe(self) -> None:
        """Configura o ``QUALIAQuantumUniverse`` se necessário."""
        if self.qualia_universe is not None:
            logger.info("Reutilizando instância existente de QUALIAQuantumUniverse")
            return
        universe_config = self.config.get("universe_config", {})
        ace_cfg = self.loaded_strategy_configs.get("ace_config", {})
        qmc_cfg = self.loaded_strategy_configs.get("qmc_config", {})

        encoders_cfg = qmc_cfg.get(
            "encoders", self.loaded_strategy_configs.get("encoders", [])
        )
        if not isinstance(encoders_cfg, list):
            encoders_cfg = []

        qubits_normal = ace_cfg.get("qubits_normal")
        if qubits_normal is None:
            qubits_normal = len(encoders_cfg)

        n_qubits_for_universe = universe_config.get("n_qubits", qubits_normal or 8)

        self.qualia_universe = QUALIAQuantumUniverse(
            n_qubits=n_qubits_for_universe,
            scr_depth=universe_config.get("scr_depth", 10),
            base_lambda=universe_config.get("base_lambda", 0.5),
            alpha=universe_config.get("alpha", 0.1),
            retro_strength=universe_config.get("retro_strength", 0.1),
            num_ctc_qubits=universe_config.get("num_ctc_qubits", 0),
            measure_frequency=universe_config.get("measure_frequency", 1),
            thermal_coefficient=universe_config.get("thermal_coefficient", 0.01),
            backend_name=universe_config.get(
                "backend_name", "aer_simulator_statevector"
            ),
            qast_feedback_enabled=universe_config.get("qast_feedback_enabled", False),
            lambda_factor_multiplier=universe_config.get(
                "lambda_factor_multiplier", 1.0
            ),
            enrichment_cycles=universe_config.get("enrichment_cycles", 1),
            adaptive_threshold=universe_config.get("adaptive_threshold", None),
            entanglement_style=universe_config.get("entanglement_style", "linear"),
            max_history_size=universe_config.get("max_history_size", 100),
            initial_state_type=universe_config.get("initial_state_type", "qft"),
            eqci_config=universe_config.get(
                "eqci_config",
                {
                    "critical_window": 20,
                    "secondary_window": 20,
                    "vol_cap": 3.0,
                },
            ),
            qpm_config=self.config.get("qpm_config", {}),
            qpm_instance=getattr(self, "qpm_memory", None),
        )
        logger.info(
            f"QUALIAQuantumUniverse inicializado com {self.qualia_universe.n_qubits} qubits."
        )

    def _setup_analysis_core(self) -> None:
        """Instancia o módulo de análise interna da consciência se necessário."""
        if self.qualia_analysis_core is not None:
            logger.info("Reutilizando instância existente de QualiaAnalysisCore")
            return
        qac_cfg = self.config.get("qualia_analysis_core_config", {})
        self.qualia_analysis_core = QualiaAnalysisCore(
            n_qubits=self.qualia_universe.n_qubits,
            history_maxlen=qac_cfg.get("history_maxlen", 256),
            pca_n_components_variance=qac_cfg.get("pca_n_components_variance", 0.95),
            kmeans_k_range=tuple(qac_cfg.get("kmeans_k_range", [2, 10])),
        )
        logger.info("QualiaAnalysisCore (PCA/KMeans) inicializado.")

    def _setup_qmc(self) -> None:
        """Inicializa o QuantumMetricsCalculator."""
        qmc_cfg_file = self.loaded_strategy_configs.get("qmc_config", {})

        configured_tf = qmc_cfg_file.get("trading_primary_timeframe")
        convert_mismatch = qmc_cfg_file.get("convert_mismatched_timeframes", False)
        if configured_tf and configured_tf != self.primary_timeframe:
            if convert_mismatch:
                qmc_cfg_file["trading_primary_timeframe"] = self.primary_timeframe
            else:
                logger.warning(
                    "Timeframe de execução '%s' difere da configuracao do QMC '%s'.",
                    self.primary_timeframe,
                    configured_tf,
                )
        encoders = qmc_cfg_file.get(
            "encoders", self.loaded_strategy_configs.get("encoders", [])
        )
        if not isinstance(encoders, list):
            logger.warning(
                "A configuração 'encoders' para QMC não é uma lista. Usando lista vazia."
            )
            encoders = []

        raw_to_norm = dict(zip(self.symbols_raw, self.symbols))
        if raw_to_norm:
            for group in encoders:
                enc_list = group.get("encoders", []) if isinstance(group, dict) else []
                for enc in enc_list:
                    params = enc.get("params", {}) if isinstance(enc, dict) else {}
                    keys = params.get("data_keys")
                    if not keys:
                        continue
                    single = isinstance(keys, str)
                    if single:
                        keys_list = [keys]
                    else:
                        keys_list = list(keys)
                    new_keys = []
                    for dk in keys_list:
                        new_dk = dk
                        for raw, norm in raw_to_norm.items():
                            if dk.startswith(raw + "_"):
                                new_dk = dk.replace(raw, norm, 1)
                                break
                        new_keys.append(new_dk)
                    params["data_keys"] = new_keys[0] if single else new_keys

        qmc_init_config = {
            "encoders": encoders,
            "trading_symbols": self.symbols,
            "trading_primary_timeframe": self.primary_timeframe,
            "trading_timeframes": self.timeframes,
        }
        for key, value in qmc_cfg_file.items():
            if key not in qmc_init_config:
                qmc_init_config[key] = value
        if (
            hasattr(self.qualia_universe, "thermal_coefficient")
            and "thermal_coefficient" not in qmc_init_config
        ):
            qmc_init_config["thermal_coefficient"] = (
                self.qualia_universe.thermal_coefficient
            )

        self.qmc = QuantumMetricsCalculator(
            qualia_universe=self.qualia_universe,
            config=qmc_init_config,
            qualia_consciousness_instance=self.qualia_analysis_core,
        )
        logger.info(
            "QuantumMetricsCalculator (QMC) inicializado e recebeu QualiaAnalysisCore."
        )

    def _setup_qpm(self) -> None:
        """Inicializa a ``QuantumPatternMemory`` se necessário."""
        if self.qpm_memory is not None:
            logger.info("Reutilizando instância existente de QuantumPatternMemory")
            if self.qpm_memory.risk_manager is None:
                primary_symbol = self.symbols[0] if self.symbols else None
                rm_instance = self.risk_managers.get(primary_symbol)
                if rm_instance:
                    self.qpm_memory.risk_manager = rm_instance
                    logger.info(
                        "RiskManager injetado em QuantumPatternMemory reutilizado"
                    )
                else:
                    logger.warning(
                        "Risk manager principal não encontrado para QPM reutilizado"
                    )
            return
        qpm_config = self.config.get("qpm_config", {})
        if not qpm_config and self.loaded_strategy_configs:
            qpm_config = self.loaded_strategy_configs.get("qpm_config", {})
        from .config.settings import settings

        qpm_persistence_path = setup_utils._normalize_path(
            qpm_config.get("persistence_path", settings.qpm_memory_file)
        )
        threshold = qpm_config.get("similarity_threshold", 0.4)
        env_threshold = os.getenv("QPM_SIMILARITY_THRESHOLD")
        if env_threshold is not None:
            try:
                threshold = float(env_threshold)
            except ValueError:
                logger.warning("QPM_SIMILARITY_THRESHOLD invalido: %s", env_threshold)

        # YAA: Corrigir a injeção de dependência do RiskManager
        primary_symbol = self.symbols[0] if self.symbols else None
        risk_manager_instance = self.risk_managers.get(primary_symbol)

        if not risk_manager_instance:
            logger.warning(
                "Risk manager principal não encontrado para QPM. Alguns recursos podem não funcionar."
            )

        self.qpm_memory = get_qpm_instance(
            {
                "max_memory_size_per_dimension": qpm_config.get(
                    "max_memory_size",
                    self.config.get("max_qpm_memory_size", 1000),
                ),
                "similarity_threshold": threshold,
                "persistence_path": qpm_persistence_path,
                "enable_warmstart": qpm_config.get("enable_warmstart", True),
                "warmstart_min_patterns": qpm_config.get("warmstart_min_patterns", 50),
                "auto_persist": qpm_config.get("auto_persist"),
                "risk_manager": risk_manager_instance,
            }
        )
        logger.info(
            f"QuantumPatternMemory (QPM) inicializada. Max size per dimension: {self.qpm_memory.max_memory_size_per_dimension}, Path: {qpm_persistence_path}"
        )
        if not (qpm_persistence_path and Path(qpm_persistence_path).exists()):
            self.qpm_memory.warm_start()

    def _setup_backtest_data(self, df: pd.DataFrame) -> None:
        """Configure backtest iterators based on ``self.primary_timeframe``."""

        self.market_data = {
            s: {self.primary_timeframe: df.copy()} for s in self.symbols
        }
        self.backtest_data_iterator = {
            s: {self.primary_timeframe: iter(df.itertuples())} for s in self.symbols
        }
        self.backtest_current_kline = {
            s: {self.primary_timeframe: None} for s in self.symbols
        }

    async def _init_system_components(self) -> None:
        logger.info("QUALIARealTimeTrader: Iniciando componentes do sistema...")
        self.system_health.component_initialization = "pending"
        logger.info(
            f"_init_system_components: self.symbols no início: {self.symbols}, self.risk_managers keys no início: {list(self.risk_managers.keys())}"
        )  # YAA: Log adicionado

        try:
            # 0. Carregar configurações (se houver)
            # self._load_strategy_parameters()  # YAA: Removido para evitar chamada duplicada
            if not self.symbols or not self.timeframes:
                logger.error(
                    "Símbolos ou timeframes não definidos após o carregamento da configuração."
                )
                raise ValueError("Símbolos ou timeframes não podem estar vazios.")

            await self._initialize_exchange_connection()
            if not self.symbols_normalized:
                try:
                    normalized = await validate_and_normalize_symbols(
                        self.symbols, self.exchange
                    )
                    if normalized:
                        if normalized != self.symbols:
                            logger.info("Símbolos normalizados: %s", normalized)
                            mapping = dict(zip(self.symbols, normalized))
                            self.symbols = normalized
                            for attr in [
                                "market_data",
                                "current_tickers",
                                "stale_tickers",
                                "position_meta",
                                "last_data_update_time",
                                "strategies_additional_tf",
                            ]:
                                data = getattr(self, attr, {})
                                setattr(
                                    self,
                                    attr,
                                    {mapping.get(k, k): v for k, v in data.items()},
                                )

                            self.last_trade_close_time = defaultdict(
                                float,
                                {
                                    mapping.get(k, k): v
                                    for k, v in self.last_trade_close_time.items()
                                },
                            )
                    else:
                        logger.warning(
                            "Nenhum símbolo válido após normalização inicial."
                        )
                    self.symbols_normalized = True
                except (ValueError, TypeError) as norm_exc:
                    logger.exception(
                        "Falha na normalização inicial de símbolos: %s",
                        norm_exc,
                    )

            for sym in self.symbols:
                self.market_data.setdefault(sym, {})
                self.current_tickers.setdefault(sym, {})
                self.stale_tickers.setdefault(sym, False)
                self.position_meta.setdefault(sym, {})
                self.last_data_update_time.setdefault(sym, defaultdict(lambda: None))
                self.strategies_additional_tf.setdefault(sym, {})
                self.last_trade_close_time.setdefault(sym, 0.0)

            self._setup_risk_managers()

            # Capturar stop_loss_adjustment de cada RiskManager logo apos a
            # criacao para eventual uso pelas estrategias. Isso evita warnings
            # ao passar parametros nao reconhecidos pelas dataclasses.
            self._risk_manager_sl_adj = {
                sym: getattr(rm, "stop_loss_adjustment", None)
                for sym, rm in self.risk_managers.items()
            }

            self._setup_qpm()
            self._setup_universe()
            self._setup_analysis_core()
            self._setup_qmc()

            # YAA: Passo 5 (NOVA ORDEM): Adaptive Consciousness Evolution (ACE)
            # Anteriormente item 4
            ace_config_from_json = self.loaded_strategy_configs.get("ace_config", {})
            ace_config_original_before_injection = (
                ace_config_from_json.copy()
            )  # YAA: Capturar estado original
            logger.debug(
                f"ACE config from JSON (before Trader profile injection): {ace_config_from_json}"
            )
            ace_config_from_json["risk_profile"] = self.risk_profile
            logger.info(
                f"Injected Trader risk_profile '{self.risk_profile}' into ACE config."
            )
            logger.debug(
                f"ACE config from JSON (after Trader profile injection): {ace_config_from_json}"
            )

            self.adaptive_consciousness_engine = AdaptiveConsciousnessEvolution(
                qualia_universe=self.qualia_universe,
                config=ace_config_from_json,
            )
            logger.info("AdaptiveConsciousnessEvolution (ACE) inicializado.")
            # YAA: Adicionar log se ACE usou defaults
            if (
                not ace_config_original_before_injection
            ):  # Verifica se o dict original (antes da injeção do profile) estava vazio
                logger.info(
                    "ACE foi inicializado sem configuração específica (ace_config ausente/vazio no JSON). Utilizará seus defaults internos."
                )

            if (
                self.adaptive_consciousness_engine
                and self.adaptive_consciousness_engine.is_dynamic_risk_enabled()
            ):
                logger.info(
                    "Controle de risco dinâmico habilitado via AdaptiveConsciousnessEvolution."
                )

            # YAA: Passo 6 (NOVA ORDEM): Metacognição Executiva (QUALIAMetacognitionTrading)
            # Anteriormente item 6
            metacog_config = self.config.get("metacognition_config", {})
            if not self.disable_metacognition:
                self.metacognition_executive = QUALIAMetacognitionTrading(
                    adaptive_consciousness_evolution=self.adaptive_consciousness_engine,
                    qpm_memory=self.qpm_memory,
                    initial_pnl_data=[],
                    config=metacog_config,
                    qualia_analysis_module=self.qualia_analysis_core,  # Usa a instância já criada
                    universe=self.qualia_universe,
                )
                history_path = metacog_config.get("history_path")
                if history_path:
                    self.metacognition_executive.load_history(history_path)
                logger.info("QUALIAMetacognitionTrading (Executivo) inicializado.")
            else:
                self.metacognition_executive = None
                logger.info(
                    "QUALIAMetacognitionTrading (Executivo) NÃO inicializado (disable_metacognition=True)."
                )

            # YAA: D-03.2 - Inicialização da Live Feed Integration
            await self._init_live_feed_integration()

            # 7. Pré-carregamento de dados da exchange
            symbols_before_normalization = list(self.symbols)
            if self.exchange and self.data_source not in {"mock"}:
                strat_cfg = self.loaded_strategy_configs.get("strategy_config", {})
                preload_primary = strat_cfg.get(
                    f"preload_candles_{self.primary_timeframe}",
                    MIN_INITIAL_HISTORY,
                )
                preload_5m = max(
                    strat_cfg.get("preload_candles_5m", MIN_INITIAL_HISTORY),
                    MIN_INITIAL_HISTORY,
                )

                # Verificar novamente a normalização caso a exchange
                # tenha modificado os símbolos retornados. Qualquer
                # alteração dispara uma reconfiguração do QMC.
                try:
                    normalized = await validate_and_normalize_symbols(
                        self.symbols, self.exchange
                    )
                    if normalized and normalized != self.symbols:
                        logger.info(
                            "Símbolos re-normalizados para pré-carregamento: %s",
                            normalized,
                        )
                        self.symbols = normalized
                        self._setup_qmc()
                    elif not normalized:
                        logger.warning(
                            "Nenhum símbolo válido após verificação; pré-carregamento abortado."
                        )
                except (ValueError, TypeError) as norm_exc:
                    logger.exception(
                        "Falha na verificação de normalização de símbolos: %s",
                        norm_exc,
                    )

                async def _do_preload() -> None:
                    for sym in self.symbols:
                        # YAA CORREÇÃO CRÍTICA: Aumentar significativamente o limite de preload
                        # para garantir que dados suficientes sejam carregados
                        primary_limit = max(
                            preload_primary, 1000
                        )  # Mínimo 1000 candles
                        fivem_limit = max(preload_5m, 500)  # Mínimo 500 candles para 5m

                        logger.info(
                            f"🔄 Pré-carregando {primary_limit} candles {self.primary_timeframe} para {sym}"
                        )
                        df = await self.exchange.fetch_ohlcv(
                            MarketSpec(sym, self.primary_timeframe),
                            limit=primary_limit,
                        )
                        if not isinstance(df, pd.DataFrame):
                            df = pd.DataFrame(
                                df,
                                columns=[
                                    "timestamp",
                                    "open",
                                    "high",
                                    "low",
                                    "close",
                                    "volume",
                                ],
                            )
                            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")
                        self.market_data.setdefault(sym, {})[
                            self.primary_timeframe
                        ] = df
                        logger.info(
                            "✅ %s candles %s pré-carregados para %s",
                            len(df),
                            self.primary_timeframe,
                            sym,
                        )

                        logger.info(
                            f"🔄 Pré-carregando {fivem_limit} candles 5m para {sym}"
                        )
                        df_5m = await self.exchange.fetch_ohlcv(
                            MarketSpec(sym, "5m"),
                            limit=fivem_limit,
                        )
                        if not isinstance(df_5m, pd.DataFrame):
                            df_5m = pd.DataFrame(
                                df_5m,
                                columns=[
                                    "timestamp",
                                    "open",
                                    "high",
                                    "low",
                                    "close",
                                    "volume",
                                ],
                            )
                            df_5m["timestamp"] = pd.to_datetime(
                                df_5m["timestamp"], unit="ms"
                            )
                        self.market_data.setdefault(sym, {})["5m"] = df_5m
                        logger.info(
                            "✅ %s candles 5m pré-carregados para %s",
                            len(df_5m),
                            sym,
                        )

                try:
                    await _do_preload()
                except (
                    OSError,
                    ccxt.BaseError,
                    ValueError,
                ) as preload_exc:  # noqa: BLE001 - log and continue
                    logger.exception(
                        "Erro ao pré-carregar velas de %s/5m: %s",
                        self.primary_timeframe,
                        preload_exc,
                    )

                # YAA CORREÇÃO CRÍTICA: Garantir que todos os dados históricos necessários
                # estejam carregados ANTES de criar estratégias
                await self._ensure_complete_historical_data()

            else:
                logger.info(f"Exchange não inicializada; modo {self.mode}.")
                self.exchange = None

            # Após a normalização no pré-carregamento, reconfigure o QMC caso
            # os símbolos tenham mudado. Isso garante que os encoders usem as
            # novas chaves de dados e evita mensagens de "Chave de dados não
            # encontrada" durante o encoding.
            if (
                self.symbols != symbols_before_normalization
                and getattr(self, "qmc", None) is not None
            ):
                logger.info(
                    "Símbolos alterados para %s; recriando QMC com valores normalizados.",
                    self.symbols,
                )
                self._setup_qmc()

            # 8. Risk Managers e Estratégias de Trading (incluindo QAST)
            for symbol in self.symbols:
                logger.info(
                    f"_init_system_components: Processando símbolo: {symbol} para RiskManager e Estratégia"
                )  # YAA: Log adicionado
                # Tenta carregar config específica do símbolo; se não houver, usa a config global de estratégia
                symbol_specific_config = self.loaded_strategy_configs.get(symbol, {})
                global_strategy_config = self.loaded_strategy_configs.get(
                    "strategy_config", {}
                )

                # YAA: Definir o default_global_alias ANTES de usá-lo.
                default_global_alias = self.config.get(
                    "default_strategy_alias", "NovaEstrategiaQUALIA"
                )

                # Prioriza alias do config específico do símbolo, depois nome do config global, depois default global
                current_strategy_alias = symbol_specific_config.get(
                    "strategy_alias",
                    global_strategy_config.get("name", default_global_alias),
                )

                # Os parâmetros da estratégia também devem seguir uma lógica similar de prioridade
                # Aqui, vamos assumir que os parâmetros relevantes para a estratégia estão em 'params' dentro da config da estratégia.
                strategy_params_for_symbol = symbol_specific_config.get(
                    "params", global_strategy_config.get("params", {})
                )

                # YAA: RESTAURADO - Configuração do RiskManager e obtenção de qast_config
                rp_entry = self.risk_profile_configs.get(self.risk_profile, {})
                if isinstance(rp_entry, dict):
                    risk_profile_params = rp_entry.get(
                        symbol, rp_entry.get("default", {})
                    )
                else:
                    risk_profile_params = rp_entry

                # Esta parte já foi executada na inicialização de risk managers anterior
                # YAA: Verificar se o risk manager já foi criado
                if symbol not in self.risk_managers:
                    message = (
                        f"RiskManager para {symbol} não foi inicializado. "
                        "Execute _setup_risk_managers ou configure 'risk_profile_configs' "
                        "corretamente para criar o RiskManager."
                    )
                    logger.error(message)
                    raise ValueError(message)

                logger.info(f"Usando RiskManager já inicializado para {symbol}")

                # Obter stop_loss_adjustment previamente capturado do RiskManager
                # para uso como multiplicador ATR (k)
                atr_k_from_risk_manager = self._risk_manager_sl_adj.get(symbol)
                params_dc = get_params_dataclass_for_alias(current_strategy_alias)

                if (
                    params_dc is not None
                    and "atr_sl_multiplier_k" in params_dc.__annotations__
                    and atr_k_from_risk_manager is not None
                ):
                    strategy_params_for_symbol["atr_sl_multiplier_k"] = (
                        atr_k_from_risk_manager
                    )

                qast_config_for_symbol = strategy_params_for_symbol.get(
                    "qast_config", self.config.get("qast_default_config", {})
                )

                # YAA: RESTAURADO - Construção do shared_context
                # P-4 CORREÇÃO: Garantir que qpm_instance está sempre presente
                if not self.qpm_memory:
                    logger.warning(
                        f"QPM não foi inicializado. Criando QPM stub para {symbol}"
                    )
                    self.qpm_memory = get_qpm_instance(
                        {
                            "max_memory_size_per_dimension": 100,
                            "risk_manager": self.risk_managers.get(symbol),
                        }
                    )

                current_shared_context = {
                    "qualia_consciousness_module": self.qualia_analysis_core,
                    "symbol": symbol,
                    "timeframe": self.primary_timeframe,
                    "trader_mode": self.mode,
                    "risk_profile": self.risk_profile,
                    "qualia_config": self.loaded_strategy_configs,
                    "metacognition_executive": self.metacognition_executive,
                    "qpm_instance": self.qpm_memory,  # P-4: Garantir QPM sempre presente
                    "quantum_metrics_calculator": self.qmc,
                    "preloaded_candles_primary": self.market_data.get(symbol, {}).get(
                        self.primary_timeframe
                    ),
                    "risk_manager": self.risk_managers[symbol],
                }

                if symbol in self.strategies:
                    logger.info(
                        "Estratégia principal para %s já existente; criação pulada.",
                        symbol,
                    )
                    main_strategy_instance = self.strategies[symbol]
                    qast_engine_instance = self.qast_engines.get(symbol)
                elif (
                    not self.loaded_strategy_configs or symbol not in self.risk_managers
                ):
                    logger.error(
                        "Configurações ou RiskManager ausentes para %s; estratégia não criada.",
                        symbol,
                    )
                    continue
                else:
                    # YAA TASK 5: Usar Registry pattern para criação de estratégias
                    try:
                        from .strategies.instance_registry import get_strategy_registry

                        registry = get_strategy_registry()

                        # Extrair timeframe do contexto ou usar padrão
                        timeframe = current_shared_context.get("timeframe", "1h")

                        # Usar Registry para obter ou criar estratégia
                        main_strategy_instance = registry.get_or_create_strategy(
                            alias=current_strategy_alias,
                            symbol=symbol,
                            timeframe=timeframe,
                            params=strategy_params_for_symbol,
                            context=current_shared_context,
                            config_manager=None,
                            force_new=False  # Reutilizar se existir
                        )

                        # Criar QAST engine separadamente (não gerenciado pelo Registry)
                        from .strategies.strategy_utils import create_qast_engine
                        qast_engine_instance = create_qast_engine(
                            qast_params=qast_config_for_symbol,
                            shared_context=current_shared_context
                        )

                        self.strategies[symbol] = main_strategy_instance
                        self.qast_engines[symbol] = qast_engine_instance

                        logger.info(f"✅ Estratégia para {symbol} criada via Registry pattern")

                    except Exception as e:
                        logger.error(f"❌ Erro ao criar estratégia via Registry para {symbol}: {e}")
                        # Fallback para método tradicional
                        logger.info(f"🔄 Usando fallback tradicional para {symbol}")
                        (
                            main_strategy_instance,
                            qast_engine_instance,
                        ) = create_strategy_and_qast_engine(
                            strategy_alias=current_strategy_alias,
                            strategy_params=strategy_params_for_symbol,
                            qast_params=qast_config_for_symbol,
                            shared_context=current_shared_context,
                        )
                        self.strategies[symbol] = main_strategy_instance
                        self.qast_engines[symbol] = qast_engine_instance

                # YAA: RESTAURADO - Logging e carregamento de dados históricos para QAST
                logger.info(
                    f"Estratégia principal e QASTEngine para {symbol} no timeframe {self.primary_timeframe} inicializados."
                )
                if qast_engine_instance:
                    if self.qast_historical_data_path:
                        try:
                            hist_data_file = (
                                Path(self.qast_historical_data_path)
                                / f"{symbol.replace('/', '_')}_{self.primary_timeframe}.csv"
                            )
                            if hist_data_file.exists():
                                hist_df = pd.read_csv(
                                    hist_data_file,
                                    index_col="timestamp",
                                    parse_dates=True,
                                )
                                if hasattr(
                                    qast_engine_instance,
                                    "load_historical_data",
                                ):
                                    qast_engine_instance.load_historical_data(hist_df)
                                    logger.info(
                                        f"Dados históricos de {hist_data_file} carregados para QASTEngine de {symbol}."
                                    )
                                else:
                                    logger.warning(
                                        f"QASTEngine para {symbol} não possui método 'load_historical_data'. Dados não carregados."
                                    )
                            else:
                                logger.warning(
                                    f"Arquivo de dados históricos {hist_data_file} não encontrado para QASTEngine de {symbol}."
                                )
                        except (
                            OSError,
                            ValueError,
                            pd.errors.ParserError,
                        ) as e_hist_data:
                            logger.exception(
                                "Erro ao carregar dados históricos para QASTEngine de %s: %s",
                                symbol,
                                e_hist_data,
                            )

                    if not qast_engine_instance.population:
                        qast_engine_instance.initialize_population()
                        logger.info(f"População QAST para {symbol} inicializada.")

                # YAA: RESTAURADO - Configuração de estratégias para timeframes adicionais
                self.strategies_additional_tf.setdefault(symbol, {})
                factory = StrategyFactory()  # Instanciar a fábrica

                for tf_add in self.timeframes:
                    if tf_add == self.primary_timeframe:
                        continue

                    if tf_add in self.strategies_additional_tf[symbol]:
                        logger.info(
                            "Estratégia adicional %s para %s já existe; criação pulada.",
                            tf_add,
                            symbol,
                        )
                        continue

                    additional_strategy_params = strategy_params_for_symbol.copy()
                    additional_strategy_params["timeframe"] = tf_add

                    additional_shared_context = current_shared_context.copy()
                    additional_shared_context["timeframe"] = tf_add
                    additional_shared_context["qualia_config"] = (
                        self.loaded_strategy_configs
                    )

                    try:
                        self.strategies_additional_tf[symbol][tf_add] = (
                            factory.create_strategy(
                                alias=current_strategy_alias,
                                params=additional_strategy_params,
                                context=additional_shared_context,
                            )
                        )
                        logger.info(
                            f"Estratégia para {symbol} no timeframe adicional {tf_add} inicializada via Factory."
                        )
                    except (RuntimeError, ValueError, KeyError) as e_strat_add:
                        logger.exception(
                            "Erro ao criar estratégia para %s no timeframe adicional %s via Factory: %s",
                            symbol,
                            tf_add,
                            e_strat_add,
                        )

            # Validação prévia de limites antes da validação de histórico
            await self._validate_exchange_limits()
            await self._validate_initial_history()

            logger.info(
                "Todos os componentes do sistema QUALIA foram inicializados com sucesso."
            )
            self.system_health.component_initialization = "success"
            logger.info(
                f"_init_system_components COMPLETED. Chaves finais em self.risk_managers: {list(self.risk_managers.keys())}"
            )  # YAA: Log adicionado

        except (RuntimeError, ValueError, KeyError) as e:
            logger.critical(
                "Falha crítica durante a inicialização dos componentes do QUALIA",
                exc_info=True,
                extra={"context": {"error": str(e)}},
            )
            self.system_health.component_initialization = f"failed: {str(e)}"
            # Propagar a exceção para interromper a execução se a inicialização
            # falhar
            raise

    def _save_open_positions(self) -> None:
        """Persist open positions delegating to :mod:`position_storage`."""
        position_storage.save_open_positions(self)

    async def _validate_exchange_limits(self) -> None:
        """Validate que os requisitos de histórico são compatíveis com limites da exchange."""

        for symbol, strategy in self.strategies.items():
            required = getattr(strategy, "required_initial_data_length", 0)
            if required <= 0:
                continue

            if self.exchange and hasattr(self.exchange, "max_history_candles"):
                try:
                    available = await self.exchange.max_history_candles(
                        symbol, self.primary_timeframe
                    )

                    if available < required:
                        logger.warning(
                            "Requisito de histórico (%s) excede disponibilidade da exchange (%s) para %s@%s",
                            required,
                            available,
                            symbol,
                            self.primary_timeframe,
                        )

                        # Tentativa de ajuste automático se a estratégia suporta
                        if hasattr(strategy, "s3_tsvf_window") and available > 0:
                            original_window = strategy.s3_tsvf_window
                            adjusted_window = max(
                                1, int(original_window * available / required)
                            )
                            strategy.s3_tsvf_window = adjusted_window

                            logger.info(
                                "Auto-ajustando s3_tsvf_window de %s para %s baseado em limit=%s",
                                original_window,
                                adjusted_window,
                                available,
                            )

                            # Recalcular requisito após ajuste
                            required = getattr(
                                strategy, "required_initial_data_length", required
                            )

                        if available < required:
                            raise DataRequirementsError(
                                f"Exchange {self.data_source} fornece apenas {available} candles "
                                f"para {symbol}@{self.primary_timeframe}, mas estratégia requer {required}"
                            )

                    logger.info(
                        "Validação OK: %s candles disponíveis vs %s requeridos para %s@%s",
                        available,
                        required,
                        symbol,
                        self.primary_timeframe,
                    )

                except InsufficientHistoryError:
                    logger.critical(
                        "Exchange não possui histórico disponível para %s@%s",
                        symbol,
                        self.primary_timeframe,
                    )
                    raise

    async def _validate_initial_history(self) -> None:
        """Ensure each strategy has sufficient preloaded history.

        YAA CORREÇÃO: Versão otimizada usando AdaptiveHistoryManager
        """

        for symbol, strategy in self.strategies.items():
            required = getattr(strategy, "required_initial_data_length", 0)
            if required <= 0:
                continue

            # YAA CORREÇÃO: Registrar requisito no gerenciador adaptativo
            requirement = HistoryRequirement(
                symbol=symbol,
                timeframe=self.primary_timeframe,
                strategy_name=getattr(strategy, "name", strategy.__class__.__name__),
                required_candles=required,
                min_acceptable=max(MIN_INITIAL_HISTORY, required // 2),
                priority=1,
            )
            self.adaptive_history_manager.register_strategy_requirement(requirement)

            df = self.market_data.get(symbol, {}).get(self.primary_timeframe)
            available = len(df) if isinstance(df, pd.DataFrame) else 0
            shared = getattr(strategy, "shared_context", {}) or {}

            # YAA CORREÇÃO: Usar gerenciador adaptativo para obter dados
            if available < required:
                logger.info(
                    f"Histórico insuficiente para {symbol}@{self.primary_timeframe}: "
                    f"{available}/{required}. Usando gerenciador adaptativo..."
                )

                try:
                    # Obter dados usando o gerenciador adaptativo
                    (
                        data,
                        metadata,
                    ) = await self.adaptive_history_manager.get_historical_data(
                        symbol=symbol,
                        timeframe=self.primary_timeframe,
                        required_candles=required,
                        exchange_fetcher=self.exchange,
                        exchange_name=self.data_source,
                        force_refresh=False,
                    )

                    if not data.empty:
                        # Atualizar market_data
                        self.market_data.setdefault(symbol, {})[
                            self.primary_timeframe
                        ] = data
                        available = len(data)

                        logger.debug(
                            f"Dados obtidos via gerenciador adaptativo: {available} candles. "
                            f"Metadata: {metadata}"
                        )

                        # Log estatísticas do cache
                        stats = self.adaptive_history_manager.get_cache_statistics()
                        logger.debug(
                            f"Cache stats: hit_rate={stats['hit_rate_percent']:.1f}%"
                        )
                    else:
                        logger.warning(
                            f"Gerenciador adaptativo retornou dados vazios para {symbol}"
                        )

                except (
                    ccxt.NetworkError,
                    ccxt.ExchangeError,
                    RuntimeError,
                    ValueError,
                ) as e:
                    logger.error(f"Erro no gerenciador adaptativo para {symbol}: {e}")

            # Validação final
            df = self.market_data.get(symbol, {}).get(self.primary_timeframe)
            available = len(df) if isinstance(df, pd.DataFrame) else 0

            if available < required:
                # YAA CORREÇÃO: Tentar ajuste automático da estratégia
                if hasattr(strategy, "s3_tsvf_window") and available > 0:
                    original_window = strategy.s3_tsvf_window
                    # Calcular fator de redução baseado nos dados disponíveis
                    reduction_factor = available / required
                    new_window = max(1, int(original_window * reduction_factor))

                    if new_window < original_window:
                        logger.warning(
                            f"Auto-ajustando s3_tsvf_window de {original_window} para {new_window} "
                            f"devido a dados limitados ({available}/{required})"
                        )
                        strategy.s3_tsvf_window = new_window
                        if isinstance(getattr(strategy, "parameters", None), dict):
                            strategy.parameters["s3_tsvf_window"] = new_window

                        # Recalcular requisito após ajuste
                        required = getattr(
                            strategy, "required_initial_data_length", required
                        )

            # Verificação final
            if available < required:
                if shared.get("auto_download_history"):
                    logger.warning(
                        f"{symbol}@{self.primary_timeframe} possui {available}/{required} candles "
                        f"após otimização. Prosseguindo devido auto_download_history."
                    )
                else:
                    raise DataRequirementsError(
                        f"{symbol}@{self.primary_timeframe} possui {available}/{required} candles "
                        f"após tentativas de otimização"
                    )

            # Executar setup da estratégia
            setup_cb = getattr(strategy, "setup", None)
            if callable(setup_cb):
                try:
                    setup_cb(df)
                    logger.info(f"Setup concluído para {symbol} com {len(df)} candles")
                except (ValueError, RuntimeError, KeyError) as e:
                    logger.error(f"Erro no setup da estratégia para {symbol}: {e}")

    def _save_risk_metrics(self, symbol: str) -> None:
        """Persist risk manager metrics for a given symbol to JSON."""
        try:
            from .config import config

            Path(config.results_dir).mkdir(parents=True, exist_ok=True)
            file_path = (
                Path(config.results_dir)
                / f"risk_metrics_{symbol.replace('/', '_')}.json"
            )

            risk_manager = self.risk_managers.get(symbol)
            if risk_manager is None:
                logger.warning(
                    "Nao ha risk manager para %s ao salvar metricas de risco",
                    symbol,
                )
                return

            data = {
                "capital_history": list(risk_manager.capital_history),
                "trade_history": list(risk_manager.trade_history),
                "drawdown_history": risk_manager.drawdown_history,
                "position_sizing_history": list(risk_manager.position_sizing_history),
                "current_capital": risk_manager.current_capital,
                "initial_capital": risk_manager.initial_capital,
                "current_drawdown_pct": risk_manager.current_drawdown_pct,
            }

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, default=str)

            logger.info("Metricas de risco salvas em %s", file_path)
        except OSError as exc:
            logger.error(
                "Erro ao salvar metricas de risco em %s: %s",
                file_path if "file_path" in locals() else "unknown",
                exc,
                exc_info=True,
            )

    def _load_open_positions(self) -> None:
        """Load open positions delegating to :mod:`position_storage`."""
        position_storage.load_open_positions(self)

    async def _handle_open_positions_on_shutdown(self) -> None:
        """Close or persist all open positions when shutting down."""
        if not self.open_positions:
            return
        logger.info("Processando posições abertas para encerramento...")
        for symbol, positions in list(self.open_positions.items()):
            for pos in positions[:]:
                try:
                    current_price = pos.entry_price
                    ticker = self.current_tickers.get(symbol)
                    if ticker:
                        current_price = float(
                            ticker.get("bid") or ticker.get("ask") or current_price
                        )
                    await self._close_position(
                        symbol,
                        pos.order_id,
                        current_price,
                        "shutdown",
                    )
                except (
                    RuntimeError,
                    OSError,
                    ValueError,
                ) as exc:  # pragma: no cover - erro logado
                    logger.exception(
                        "Erro ao fechar posição %s para %s durante shutdown: %s",
                        pos.order_id,
                        symbol,
                        exc,
                    )

        if any(self.open_positions.values()):  # Persist if something failed
            position_storage.handle_open_positions_on_shutdown(self)

    async def _sync_open_positions(self) -> None:
        """Sincroniza as posições locais com o estado real da exchange."""

        await position_manager.sync_open_positions(self)

    async def _update_market_data(self, trace_id: Optional[str] = None) -> None:
        """Atualiza os dados de mercado para todos os símbolos e timeframes.

        Parameters
        ----------
        trace_id : Optional[str]
            ID de rastreamento opcional propagado para métricas e logs.
        """

        async def _do_update() -> None:
            if trace_id:
                logger.info(
                    "TraceID: %s - Iniciando atualização de dados de mercado...",
                    trace_id,
                )
            else:
                logger.info("Iniciando atualização de dados de mercado...")

            if self.exchange:
                logger.debug("Buscando dados da exchange Kraken...")
                tasks = []
                for symbol in self.symbols:
                    for timeframe in self.timeframes:
                        if trace_id:
                            tasks.append(
                                self._fetch_ohlcv_with_logging(
                                    symbol, timeframe, trace_id=trace_id
                                )
                            )
                        else:
                            tasks.append(
                                self._fetch_ohlcv_with_logging(symbol, timeframe)
                            )
                    if self.fetch_ticker_enabled and self.mode in [
                        "live",
                        "paper_trading",
                    ]:
                        if trace_id:
                            tasks.append(
                                self._fetch_and_store_ticker(symbol, trace_id=trace_id)
                            )
                        else:
                            tasks.append(self._fetch_and_store_ticker(symbol))
                if tasks:
                    logger.debug(
                        "Iniciando gather para %d tarefas com timeout de %.1fs",
                        len(tasks),
                        self.market_data_timeout,
                    )
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    failed_count = sum(
                        isinstance(result, Exception) for result in results
                    )
                    if failed_count:
                        logger.warning(
                            "%d tarefas de coleta de dados falharam.", failed_count
                        )
                        for result in results:
                            if isinstance(result, Exception):
                                logger.error(
                                    "Task error during market data update: %s",
                                    result,
                                )

                for symbol in self.symbols:
                    strategy = self.strategies.get(symbol)
                    if not strategy:
                        continue
                    required = getattr(strategy, "required_initial_data_length", 0)
                    for timeframe in self.timeframes:
                        if timeframe_to_minutes(timeframe) < 60:
                            continue
                        df = self.market_data.get(symbol, {}).get(timeframe)
                        if df is None:
                            continue
                        if not isinstance(df, pd.DataFrame):
                            logger.warning(
                                "Dados de mercado %s@%s nao estao em DataFrame: %s. Convertendo.",
                                symbol,
                                timeframe,
                                type(df),
                            )
                            df = pd.DataFrame(
                                df,
                                columns=[
                                    "timestamp",
                                    "open",
                                    "high",
                                    "low",
                                    "close",
                                    "volume",
                                ],
                            )
                            self.market_data[symbol][timeframe] = df
                        have = len(df)
                        if have < required:
                            missing = required - have
                            logger.info(
                                "%s@%s possui apenas %s/%s candles. Buscando %s adicionais.",
                                symbol,
                                timeframe,
                                have,
                                required,
                                missing,
                            )
                            await self._fetch_ohlcv_history(symbol, timeframe, missing)
            else:
                logger.error("Exchange não inicializada; impossivel obter dados.")

        start_time = time.perf_counter()
        try:
            await asyncio.wait_for(_do_update(), timeout=self.market_data_timeout)
        except asyncio.TimeoutError:
            if self.statsd:
                self.statsd.increment("market_data.update.timeout")
            raise
        finally:
            elapsed_ms = (time.perf_counter() - start_time) * 1000
            if self.statsd:
                tags = [f"trace_id:{trace_id}"] if trace_id else None
                self.statsd.timing("market_data.update.latency", elapsed_ms)
                self.statsd.timing("trading.data_collection_ms", elapsed_ms, tags=tags)
                self.statsd.increment("trading.data_collection_count", tags=tags)

    async def _fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        """Fetch ticker data with retries.

        If all attempts fail, a recently cached ticker is returned when
        available. A ``TickerFetchError`` is raised with elapsed time
        information when no ticker can be retrieved.
        """
        from datadog import DogStatsd

        if not self.exchange:
            raise TickerFetchError(
                f"Tentativa de buscar ticker para {symbol} sem exchange configurada."
            )

        exchange_obj = getattr(self.exchange, "exchange", None)
        if exchange_obj is not None and isinstance(
            getattr(exchange_obj, "markets", None), dict
        ):
            try:
                symbol = normalize_symbol(symbol, exchange_obj)
            except ValueError as exc:
                raise TickerFetchError(str(exc)) from exc

        # --- Backoff progressivo após muitas falhas ---
        if self._consecutive_ticker_failures >= self.ticker_failure_limit:
            base_pause = self.config.get("ticker_failure_pause", 5.0)
            max_pause = self.config.get("ticker_failure_max_pause", 60.0)
            extra = self._consecutive_ticker_failures - self.ticker_failure_limit + 1
            pause_time = min(base_pause * extra, max_pause)
            level = (
                logging.WARNING
                if self._consecutive_ticker_failures == self.ticker_failure_limit
                else logging.DEBUG
            )
            logger.log(
                level,
                "Pausando tentativas de ticker por %.2fs apos %s falhas.",
                pause_time,
                self._consecutive_ticker_failures,
            )
            logger.debug(
                "[TICKER] Iniciando pausa por falhas consecutivas (%s) por %.2fs às %.3f",
                self._consecutive_ticker_failures,
                pause_time,
                time.time(),
            )
            self.system_health.ticker_fetch = f"paused ({pause_time}s)"
            await asyncio.sleep(pause_time)
            logger.debug(
                "[TICKER] Fim da pausa por falhas consecutivas às %.3f",
                time.time(),
            )

            attempts_reconnect = self.ticker_reconnect_attempts
            wait_between = self.config.get("ticker_reconnect_wait", 1.0)
            reconnected = False
            for attempt in range(1, attempts_reconnect + 1):
                try:
                    self.system_health.ticker_fetch = "reconnecting"
                    if not await self._is_exchange_connected():
                        logger.info(
                            "Recriando conexao com a exchange apos falhas consecutivas (tentativa %s/%s)",
                            attempt,
                            attempts_reconnect,
                        )
                        await self._initialize_exchange_connection()
                    if await self._is_exchange_connected():
                        reconnected = True
                        break
                except (
                    OSError,
                    RuntimeError,
                    ValueError,
                ) as exc:  # pragma: no cover - log and continue
                    logger.exception(
                        "Erro ao tentar recriar conexao com a exchange: %s",
                        exc,
                    )
                if attempt < attempts_reconnect:
                    await asyncio.sleep(wait_between)

            if not reconnected:
                logger.error(
                    "Falha critica ao restabelecer conexao apos %s tentativas",
                    attempts_reconnect,
                )
                self.system_health.ticker_fetch = "critical"
            else:
                self.system_health.ticker_fetch = "retrying"

        attempts = self.config.get("ticker_retry_attempts", self.TICKER_RETRY_ATTEMPTS)
        wait_seconds = self.config.get("ticker_retry_wait", 0.5)
        last_exc: Optional[Exception] = None
        start_time = time.time()

        elapsed = 0.0
        for attempt in range(1, attempts + 1):
            attempt_start = time.time()
            logger.debug(
                "[TICKER] Inicio da tentativa %s/%s para %s às %.3f",
                attempt,
                attempts,
                symbol,
                attempt_start,
            )
            try:
                ticker = await self.exchange.fetch_ticker(symbol)
                elapsed = time.time() - attempt_start
                if ticker:
                    level = (
                        logging.INFO if self.verbose_ticker_logging else logging.DEBUG
                    )
                    logger.log(
                        level,
                        "[TICKER] Tentativa %s/%s bem-sucedida para %s em %.2fs: %s",
                        attempt,
                        attempts,
                        symbol,
                        elapsed,
                        {
                            k: ticker.get(k)
                            for k in ["bid", "ask", "last", "close", "symbol"]
                            if k in ticker
                        },
                    )
                    self._consecutive_ticker_failures = 0
                    self._ticker_failure_notified = False
                    self.system_health.ticker_fetch = "ok"
                    return ticker
                last_exc = TickerFetchError(f"Ticker retornou None para {symbol}")
                level = (
                    logging.DEBUG
                    if attempt < attempts
                    else (
                        logging.WARNING
                        if self._consecutive_ticker_failures < self.ticker_failure_limit
                        else logging.DEBUG
                    )
                )
                logger.log(
                    level,
                    "[TICKER] Ticker vazio para %s na tentativa %s/%s após %.2fs",
                    symbol,
                    attempt,
                    attempts,
                    elapsed,
                )
                if attempt < attempts:
                    delay = (
                        wait_seconds * (2 ** (attempt - 1)) * random.uniform(1.0, 1.5)
                    )
                    await asyncio.sleep(delay)
            except asyncio.TimeoutError as exc:
                elapsed = time.time() - attempt_start
                last_exc = exc
                level = (
                    logging.DEBUG
                    if attempt < attempts
                    else (
                        logging.WARNING
                        if self._consecutive_ticker_failures < self.ticker_failure_limit
                        else logging.DEBUG
                    )
                )
                logger.log(
                    level,
                    "[TICKER] Timeout na tentativa %s/%s para %s após %.2fs: %s",
                    attempt,
                    attempts,
                    symbol,
                    elapsed,
                    exc,
                )
                if attempt < attempts:
                    delay = (
                        wait_seconds * (2 ** (attempt - 1)) * random.uniform(1.0, 1.5)
                    )
                    await asyncio.sleep(delay)
            except (OSError, RuntimeError, ValueError) as exc:
                elapsed = time.time() - attempt_start
                last_exc = exc
                level = (
                    logging.DEBUG
                    if attempt < attempts
                    else (
                        logging.WARNING
                        if self._consecutive_ticker_failures < self.ticker_failure_limit
                        else logging.DEBUG
                    )
                )
                logger.log(
                    level,
                    "[TICKER] Exceção na tentativa %s/%s para %s após %.2fs: %s: %s",
                    attempt,
                    attempts,
                    symbol,
                    elapsed,
                    type(exc).__name__,
                    exc,
                )
                if attempt < attempts:
                    delay = (
                        wait_seconds * (2 ** (attempt - 1)) * random.uniform(1.0, 1.5)
                    )
                    await asyncio.sleep(delay)
            except (ccxt.NetworkError, ccxt.ExchangeError) as exc:
                elapsed = time.time() - attempt_start
                last_exc = exc
                level = (
                    logging.DEBUG
                    if attempt < attempts
                    else (
                        logging.WARNING
                        if self._consecutive_ticker_failures < self.ticker_failure_limit
                        else logging.DEBUG
                    )
                )
                logger.log(
                    level,
                    "[TICKER] Exce\u00e7\u00e3o desconhecida na tentativa %s/%s para %s ap\u00f3s %.2fs: %s: %s",
                    attempt,
                    attempts,
                    symbol,
                    elapsed,
                    type(exc).__name__,
                    exc,
                )
                if attempt < attempts:
                    delay = (
                        wait_seconds * (2 ** (attempt - 1)) * random.uniform(1.0, 1.5)
                    )
                    await asyncio.sleep(delay)
            finally:
                logger.debug(
                    "[TICKER] Fim da tentativa %s/%s para %s às %.3f (%.2fs)",
                    attempt,
                    attempts,
                    symbol,
                    time.time(),
                    elapsed,
                )

        self._consecutive_ticker_failures += 1
        if (
            self._consecutive_ticker_failures >= self.ticker_failure_limit
            and not self._ticker_failure_notified
        ):
            level = (
                logging.WARNING
                if self._consecutive_ticker_failures == self.ticker_failure_limit
                else logging.DEBUG
            )
            logger.log(
                level,
                "Falhas consecutivas ao buscar ticker para %s: %s. Ultimo erro: %s",
                symbol,
                self._consecutive_ticker_failures,
                last_exc,
            )
            self._ticker_failure_notified = True
        if self.system_health.get("ticker_fetch") != "critical":
            self.system_health.ticker_fetch = (
                f"failed ({self._consecutive_ticker_failures})"
            )
        # Antes de falhar definitivamente, verificar se há ticker recente em cache
        ticker_cache = getattr(self.exchange, "ticker_cache", None)
        if isinstance(ticker_cache, dict):
            try:
                norm = normalize_symbol(symbol, self.exchange.exchange)
            except ValueError:
                norm = symbol
            cached = ticker_cache.get(norm)
            if cached:
                cache_age = time.time() - cached[1]
                ttl = getattr(self.exchange, "ticker_cache_ttl", 5.0)
                if cache_age < ttl:
                    logger.debug(
                        "[TICKER] Usando ticker em cache para %s após falha (idade %.2fs)",
                        symbol,
                        cache_age,
                    )
                    self.system_health.ticker_fetch = "ok"
                    return cached[0]

        err_type = type(last_exc).__name__ if last_exc else "UnknownError"
        total_elapsed = time.time() - start_time
        if self.statsd:
            self.statsd.increment("ticker.fail")
        raise TickerFetchError(
            f"Falha ao obter ticker para {symbol} apos {attempts} tentativas em {total_elapsed:.2f}s: {err_type}: {last_exc}"
        ) from last_exc

    def _get_cached_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Return cached ticker data for ``symbol`` if available."""

        ticker_cache = getattr(self.exchange, "ticker_cache", None)
        if isinstance(ticker_cache, dict):
            try:
                norm = normalize_symbol(symbol, self.exchange.exchange)
            except ValueError:
                norm = symbol
            cached = ticker_cache.get(norm)
            if cached:
                return cached[0]
        return None

    def _use_ohlcv_fallback(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Use last OHLCV close value for ``symbol`` as ticker data."""

        df = self.market_data.get(symbol, {}).get(self.primary_timeframe)
        if isinstance(df, pd.DataFrame) and not df.empty:
            last_close = float(df["close"].iloc[-1])
            ticker_data = {"symbol": symbol, "close": last_close}
            self.current_tickers[symbol] = ticker_data
            self.stale_tickers[symbol] = True
            return ticker_data
        return None

    def _handle_ticker_error(self, symbol: str, exc: Exception) -> None:
        """Log ticker fetch error and update ``system_health``."""

        original = getattr(exc, "__cause__", None)
        error_type = type(exc).__name__
        if original:
            logger.debug(
                "Erro ao buscar ticker para %s [%s]: %s | Original: %s: %s",
                symbol,
                error_type,
                exc,
                type(original).__name__,
                original,
                exc_info=True,
            )
            msg = (
                f"{symbol} [{error_type}] {exc} | {type(original).__name__}: {original}"
            )
        else:
            logger.debug(
                "Erro ao buscar ticker para %s [%s]: %s",
                symbol,
                error_type,
                exc,
                exc_info=True,
            )
            msg = f"{symbol} [{error_type}] {exc}"

        self._cycle_failure_messages.append(msg)

        self._ticker_error_count = min(
            getattr(self, "_ticker_error_count", 0) + 1, MAX_ERROR_HISTORY
        )
        self.system_health.ticker_fetch = "failed"

    async def _fetch_and_store_ticker(
        self, symbol: str, trace_id: Optional[str] = None
    ) -> None:
        """Fetch and store the latest ticker for ``symbol``.

        When ticker retrieval fails, the method logs the error type, marks
        ``self.system_health['ticker_fetch']`` as ``"failed"`` and falls back
        to the last OHLCV close value.
        """
        if not self.exchange:
            logger.warning(
                f"Tentativa de buscar ticker para {symbol} sem uma instância de exchange."
            )
            return
        start_time = time.perf_counter()
        tags = [f"symbol:{symbol}"]
        if trace_id:
            tags.append(f"trace_id:{trace_id}")
            logger.debug(
                "TraceID: %s - Iniciando busca de ticker para %s",
                trace_id,
                symbol,
            )
        else:
            logger.debug(
                "[TICKER] Iniciando busca de ticker para %s às %.3f",
                symbol,
                start_time,
            )
        try:
            ticker_data = await self._fetch_ticker(symbol)
            if ticker_data is None:
                ticker_data = self._get_cached_ticker(symbol)
            elapsed = time.perf_counter() - start_time
            self.current_tickers[symbol] = ticker_data
            self.stale_tickers[symbol] = False
            if ticker_data is None:
                logger.error(
                    "[TICKER] Ticker retornou None para %s após %.2fs (timestamp: %.3f)",
                    symbol,
                    elapsed,
                    start_time,
                )
                ticker_data = self._use_ohlcv_fallback(symbol)
                if ticker_data:
                    logger.debug(
                        "[TICKER] Usando último close OHLCV para %s: %.6f",
                        symbol,
                        ticker_data.get("close"),
                    )
            else:
                logger.debug(
                    "[TICKER] Sucesso ao buscar ticker para %s após %.2fs: %s",
                    symbol,
                    elapsed,
                    {
                        k: ticker_data.get(k)
                        for k in ["bid", "ask", "last", "close", "symbol"]
                        if k in ticker_data
                    },
                )
                logger.debug(
                    "Ticker para %s atualizado: Bid=%s, Ask=%s, Last=%s, Close=%s, Symbol=%s",
                    symbol,
                    ticker_data.get("bid"),
                    ticker_data.get("ask"),
                    ticker_data.get("last"),
                    ticker_data.get("close"),
                    ticker_data.get("symbol"),
                )
            logger.debug(
                "[TICKER] Fim da busca de ticker para %s (duração %.2fs)",
                symbol,
                elapsed,
            )
            if self.statsd:
                self.statsd.timing(
                    "ticker.fetch.latency",
                    elapsed * 1000,
                    tags=tags,
                )
                self.statsd.increment("ticker.fetch.success", tags=tags)
            if trace_id:
                logger.info(
                    "TraceID: %s - Ticker atualizado para %s em %.2fs",
                    trace_id,
                    symbol,
                    elapsed,
                )
        except asyncio.TimeoutError:
            elapsed = time.perf_counter() - start_time
            level = (
                logging.WARNING
                if self._consecutive_ticker_failures < self.ticker_failure_limit
                else logging.DEBUG
            )
            logger.log(
                level,
                "[TICKER] Tempo excedido ao buscar ticker para %s; utilizando OHLCV",
                symbol,
            )
            ticker_data = self._use_ohlcv_fallback(symbol)
            if ticker_data:
                logger.debug(
                    "[TICKER] Usando último close OHLCV para %s: %.6f",
                    symbol,
                    ticker_data.get("close"),
                )
            logger.debug(
                "[TICKER] Fim da busca de ticker para %s (duração %.2fs)",
                symbol,
                elapsed,
            )
            if self.statsd:
                self.statsd.timing(
                    "ticker.fetch.latency",
                    elapsed * 1000,
                    tags=tags,
                )
                self.statsd.increment("ticker.fetch.fail", tags=tags)
            if trace_id:
                logger.error(
                    "TraceID: %s - Timeout ao buscar ticker para %s",
                    trace_id,
                    symbol,
                )
        except TickerFetchError as exc:
            self._handle_ticker_error(symbol, exc)
            ticker_data = self._use_ohlcv_fallback(symbol)
            if ticker_data:
                level = (
                    logging.WARNING
                    if self._consecutive_ticker_failures < self.ticker_failure_limit
                    else logging.DEBUG
                )
                logger.log(
                    level,
                    "[TICKER] Usando último close OHLCV para %s após falha: %.6f",
                    symbol,
                    ticker_data.get("close"),
                )
                self._schedule_ticker_refresh(symbol)
            if self.statsd:
                self.statsd.timing(
                    "ticker.fetch.latency",
                    (time.perf_counter() - start_time) * 1000,
                    tags=tags,
                )
                self.statsd.increment("ticker.fetch.fail", tags=tags)
            if trace_id:
                logger.error(
                    "TraceID: %s - Falha ao buscar ticker para %s: %s",
                    trace_id,
                    symbol,
                    exc,
                )
        except (
            OSError,
            RuntimeError,
            ValueError,
        ) as exc:  # noqa: BLE001 - registrar e continuar
            self._handle_ticker_error(symbol, exc)
            ticker_data = self._use_ohlcv_fallback(symbol)
            if ticker_data:
                level = (
                    logging.WARNING
                    if self._consecutive_ticker_failures < self.ticker_failure_limit
                    else logging.DEBUG
                )
                logger.log(
                    level,
                    "[TICKER] Usando último close OHLCV para %s após exceção: %.6f",
                    symbol,
                    ticker_data.get("close"),
                )
            if self.statsd:
                self.statsd.timing(
                    "ticker.fetch.latency",
                    (time.perf_counter() - start_time) * 1000,
                    tags=tags,
                )
                self.statsd.increment("ticker.fetch.fail", tags=tags)
            if trace_id:
                logger.error(
                    "TraceID: %s - Erro ao buscar ticker para %s: %s",
                    trace_id,
                    symbol,
                    exc,
                )

    def _schedule_ticker_refresh(self, symbol: str) -> asyncio.Task:
        """Schedule a refresh ticker fetch for ``symbol``."""
        existing = self._pending_ticker_tasks.get(symbol)
        if existing and not existing.done():
            return existing

        task = asyncio.create_task(self._fetch_and_store_ticker(symbol))
        self._pending_ticker_tasks[symbol] = task

        def _remove(_task: asyncio.Task) -> None:
            self._pending_ticker_tasks.pop(symbol, None)

        def _log_exception(done: asyncio.Task) -> None:
            try:
                exc = done.exception()
                if exc:
                    logger.error(
                        "Erro na task de refresh de ticker para %s: %s",
                        symbol,
                        exc,
                    )
            except asyncio.CancelledError:
                logger.debug("Task de refresh de ticker cancelada")

        task.add_done_callback(_remove)
        task.add_done_callback(_log_exception)
        return task

    async def _attempt_fetch(
        self,
        symbol: str,
        timeframe: str,
        since_ms: Optional[int],
        limit: int,
        *,
        max_retries: Optional[int] = None,
    ) -> Optional[pd.DataFrame]:
        """Attempt to fetch OHLCV data with retries and exponencial backoff.

        Parameters
        ----------
        symbol
            Símbolo a ser consultado.
        timeframe
            Timeframe desejado.
        since_ms
            Timestamp inicial em milissegundos.
        limit
            Número de candles solicitados.
        max_retries
            Número máximo de tentativas. Quando ``None``, utiliza
            ``config['ohlcv_retry_attempts']``.

        Notes
        -----
        O tempo total das tentativas é limitado por ``market_data_timeout``. Se
        o limite for atingido, novas tentativas são abortadas e é registrado um
        aviso.
        """
        if max_retries is None:
            max_retries = self.config.get("ohlcv_retry_attempts", 3)
        wait_seconds = self.config.get("ohlcv_retry_wait", 1)
        start_time = time.perf_counter()

        for attempt in range(1, max_retries + 1):
            elapsed = time.perf_counter() - start_time
            if elapsed >= self.market_data_timeout:
                logger.warning(
                    "Tempo total de fetch_ohlcv excedeu limite global de %.2fs",
                    self.market_data_timeout,
                )
                break
            try:
                logger.info(
                    "Buscando %s candles OHLCV para %s@%s (tentativa %s/%s)...",
                    limit,
                    symbol,
                    timeframe,
                    attempt,
                    max_retries,
                )
                timeout = self.ohlcv_fetch_timeout_map.get(
                    (symbol, timeframe), self.ohlcv_fetch_timeout
                )
                df = await asyncio.wait_for(
                    self.exchange.fetch_ohlcv(
                        symbol,
                        timeframe,
                        since=since_ms,
                        limit=limit,
                    ),
                    timeout=timeout,
                )
                if isinstance(df, pd.DataFrame) and not df.empty:
                    return df
                logger.warning(
                    "Nenhum dado retornado da %s para %s@%s na tentativa %s/%s.",
                    self.data_source,
                    symbol,
                    timeframe,
                    attempt,
                    max_retries,
                )
            except asyncio.TimeoutError as exc:
                logger.warning(
                    "Timeout ao buscar OHLCV na tentativa %s/%s para %s@%s: %s: %s",
                    attempt,
                    max_retries,
                    symbol,
                    timeframe,
                    exc.__class__.__name__,
                    exc,
                )
            except asyncio.CancelledError as exc:
                elapsed = time.perf_counter() - start_time
                if elapsed >= self.market_data_timeout:
                    logger.warning(
                        "fetch_ohlcv cancelado devido ao market_data_timeout de %.2fs",
                        self.market_data_timeout,
                    )
                    raise
                logger.warning(
                    "fetch_ohlcv cancelado para %s@%s na tentativa %s/%s: %s",
                    symbol,
                    timeframe,
                    attempt,
                    max_retries,
                    exc,
                )
                if attempt < max_retries:
                    current_timeout = self.ohlcv_fetch_timeout_map.get(
                        (symbol, timeframe), self.ohlcv_fetch_timeout
                    )
                    new_timeout = min(current_timeout * 1.5, self.market_data_timeout)
                    self.ohlcv_fetch_timeout_map[(symbol, timeframe)] = new_timeout
                    backoff = wait_seconds * (2 ** (attempt - 1))
                    await asyncio.sleep(backoff)
                    continue
                raise
            except (
                ccxt.NetworkError,
                ccxt.ExchangeError,
                asyncio.TimeoutError,
                ValueError,
            ) as exc:
                logger.error(
                    "Falha ao buscar OHLCV na tentativa %s/%s para %s@%s: %s: %s",
                    attempt,
                    max_retries,
                    symbol,
                    timeframe,
                    type(exc).__name__,
                    exc,
                    exc_info=attempt == max_retries,
                )

            if attempt < max_retries:
                backoff = wait_seconds * (2 ** (attempt - 1))
                await asyncio.sleep(backoff)
                elapsed = time.perf_counter() - start_time
                if elapsed >= self.market_data_timeout:
                    logger.warning(
                        "Tempo total de fetch_ohlcv excedeu limite global de %.2fs",
                        self.market_data_timeout,
                    )
                    break

        return None

    def _validate_cache_age(
        self, symbol: str, timeframe: str, df: pd.DataFrame
    ) -> None:
        """Log a warning if cached OHLCV data is stale."""

        if df is None or df.empty:
            return

        last_val = df["timestamp"].iloc[-1]
        if isinstance(last_val, (pd.Timestamp, datetime)):
            last_ts = pd.Timestamp(last_val)
        else:
            last_ts = pd.Timestamp(last_val, unit="ms")

        if last_ts.tzinfo is None:
            last_ts = last_ts.tz_localize(timezone.utc)
        else:
            last_ts = last_ts.tz_convert(timezone.utc)

        age_minutes = (datetime.now(timezone.utc) - last_ts).total_seconds() / 60
        if age_minutes > self.max_cache_age_minutes:
            logger.warning(
                "Dados em cache para %s@%s desatualizados %.1f minutos (limite %s)",
                symbol,
                timeframe,
                age_minutes,
                self.max_cache_age_minutes,
            )

    def _merge_ohlcv_data(
        self,
        symbol: str,
        timeframe: str,
        existing_df: Optional[pd.DataFrame],
        fetched_df: pd.DataFrame,
        limit: Optional[int] = None,
    ) -> tuple[pd.DataFrame, int]:
        """Merge OHLCV dataframes and enforce row limit."""

        def _ensure_utc(df: pd.DataFrame) -> pd.DataFrame:
            """Return a copy with ``timestamp`` converted to UTC."""
            out = df.copy()
            ts = out["timestamp"]
            if pd.api.types.is_numeric_dtype(ts):
                out["timestamp"] = pd.to_datetime(
                    ts, unit="ms", utc=True, errors="coerce"
                )
            else:
                out["timestamp"] = pd.to_datetime(ts, utc=True, errors="coerce")
            return out

        if existing_df is not None and not existing_df.empty:
            existing_df = _ensure_utc(existing_df)
        if not fetched_df.empty:
            fetched_df = _ensure_utc(fetched_df)

        if existing_df is not None and not existing_df.empty:
            combined = pd.concat([existing_df, fetched_df], ignore_index=True)
            orig_len = len(existing_df)
        else:
            combined = fetched_df
            orig_len = 0

        combined = (
            combined.drop_duplicates(subset="timestamp", keep="last")
            .sort_values("timestamp")
            .reset_index(drop=True)
        )

        new_rows = len(combined) - orig_len
        if new_rows < 0:
            new_rows = 0

        if limit is not None and len(combined) > limit:
            combined = combined.iloc[-limit:].reset_index(drop=True)

        combined.index = pd.to_datetime(combined["timestamp"], utc=True)

        last_ts = combined.index[-1].value // 1_000_000
        self.last_ohlcv_timestamp.setdefault(symbol, {})[timeframe] = last_ts

        return combined, new_rows

    async def _fetch_ohlcv_with_logging(
        self, symbol: str, timeframe: str, trace_id: Optional[str] = None
    ) -> None:
        """Busca e armazena dados OHLCV com logging estruturado e cache."""
        # YAA: Definindo variáveis que estavam faltando no contexto
        start_time = time.perf_counter()
        tags = [f"symbol:{symbol}", f"timeframe:{timeframe}"]
        if trace_id:
            tags.append(f"trace_id:{trace_id}")

        try:
            existing_df = self.market_data.get(symbol, {}).get(timeframe)
            logger.debug(
                "Estado anterior de dados para %s@%s: %s candles",
                symbol,
                timeframe,
                len(existing_df) if existing_df is not None else 0,
            )

            # Kucoin: usar limite menor no loop principal
            if (
                self.exchange
                and hasattr(self.exchange, "exchange_id")
                and self.exchange.exchange_id == "kucoin"
            ):
                # Solicitar apenas 100 candles incrementais após carregar histórico inicial
                df = await self._attempt_fetch(symbol, timeframe, None, 100)
            else:
                df = await self._fetch_ohlcv(symbol, timeframe, trace_id=trace_id)

            if isinstance(df, pd.DataFrame) and not df.empty:
                logger.debug(
                    "Dados recebidos para %s@%s: %s candles (último: %s)",
                    symbol,
                    timeframe,
                    len(df),
                    df.index[-1] if not df.empty else "N/A",
                )

            if df is None or not isinstance(df, pd.DataFrame) or df.empty:
                advance = timeframe_to_minutes(timeframe)
                # YAA: Definindo last_saved que estava faltando
                last_saved = self.last_ohlcv_timestamp.get(symbol, {}).get(timeframe)
                since_ms = None  # YAA: Definindo since_ms que estava faltando
                target = last_saved if last_saved is not None else since_ms
                if target is not None:
                    self.last_ohlcv_timestamp.setdefault(symbol, {})[timeframe] = (
                        target + advance * 60_000
                    )
                self.last_ohlcv_new_rows.setdefault(symbol, {})[timeframe] = 0
                if existing_df is not None and not existing_df.empty:
                    self._validate_cache_age(symbol, timeframe, existing_df)
                    # YAA: Definindo max_retries que estava faltando
                    max_retries = getattr(self.exchange, "ohlcv_retries", 3)
                    logger.error(
                        "Usando dados em cache para %s@%s apos %s tentativas falhadas.",
                        symbol,
                        timeframe,
                        max_retries,
                    )
                    if self.statsd:
                        self.statsd.increment("ohlcv.fetch.fail", tags=tags)
                    if trace_id:
                        logger.error(
                            "TraceID: %s - Usando cache após falha de OHLCV %s@%s",
                            trace_id,
                            symbol,
                            timeframe,
                        )
                    return
                logger.error(
                    "Falha ao obter dados para %s@%s apos tentativas e sem cache.",
                    symbol,
                    timeframe,
                )
                if self.statsd:
                    self.statsd.increment("ohlcv.fetch.fail", tags=tags)
                if trace_id:
                    logger.error(
                        "TraceID: %s - Falha ao obter dados para %s@%s",
                        trace_id,
                        symbol,
                        timeframe,
                    )
                return

            throttled_debug(
                "Dados de %s@%s obtidos da %s: %s candles.",
                symbol,
                timeframe,
                self.data_source,
                len(df),
            )

            # YAA: Definindo limit para o merge
            limit = None
            if hasattr(self.exchange, "max_candles"):
                try:
                    limit = self.exchange.max_candles(timeframe)
                except (RuntimeError, ValueError):
                    limit = 1000

            combined, new_rows = self._merge_ohlcv_data(
                symbol, timeframe, existing_df, df, limit
            )
            throttled_debug(
                "Merge result para %s@%s: %s novos candles, total %s.",
                symbol,
                timeframe,
                new_rows,
                len(combined),
            )
            self.market_data[symbol][timeframe] = combined

            last_val = combined["timestamp"].iloc[-1]
            if isinstance(last_val, (pd.Timestamp, datetime)):
                last_ts = int(pd.Timestamp(last_val).timestamp() * 1000)
            else:
                last_ts = int(last_val)
            self.last_ohlcv_timestamp.setdefault(symbol, {})[timeframe] = last_ts
            self.last_ohlcv_new_rows.setdefault(symbol, {})[timeframe] = new_rows
        except (OSError, RuntimeError, ValueError) as exc:
            logger.error(
                "Erro ao buscar dados da Kraken para %s@%s: %s.",
                symbol,
                timeframe,
                exc,
                exc_info=True,
            )
            advance = timeframe_to_minutes(timeframe)
            # YAA: Definindo variáveis faltantes
            last_saved = self.last_ohlcv_timestamp.get(symbol, {}).get(timeframe)
            since_ms = None
            target = last_saved if last_saved is not None else since_ms
            if target is not None:
                self.last_ohlcv_timestamp.setdefault(symbol, {})[timeframe] = (
                    target + advance * 60_000
                )
            self.last_ohlcv_new_rows.setdefault(symbol, {})[timeframe] = 0
            if self.statsd:
                self.statsd.increment("ohlcv.fetch.fail", tags=tags)
            if trace_id:
                logger.error(
                    "TraceID: %s - Falha ao buscar OHLCV para %s@%s: %s",
                    trace_id,
                    symbol,
                    timeframe,
                    exc,
                )
        else:
            if self.statsd:
                self.statsd.increment("ohlcv.fetch.success", tags=tags)
            if trace_id:
                logger.info(
                    "TraceID: %s - OHLCV %s@%s obtido com sucesso",
                    trace_id,
                    symbol,
                    timeframe,
                )
        finally:
            if self.statsd:
                self.statsd.timing(
                    "ohlcv.fetch.latency",
                    (time.perf_counter() - start_time) * 1000,
                    tags=tags,
                )

    async def _fetch_ohlcv_history(
        self, symbol: str, timeframe: str, missing: int
    ) -> None:
        """YAA CORREÇÃO: Versão otimizada com cache adaptativo."""

        logger.info(f"Buscando {missing} candles históricos para {symbol}@{timeframe}")

        try:
            # Usar gerenciador adaptativo
            result = await self.adaptive_history_manager.get_historical_data(
                symbol=symbol,
                timeframe=timeframe,
                required_candles=missing,
                exchange_fetcher=self.exchange,
                exchange_name=self.data_source,
                force_refresh=True,
            )

            # Verificar se retornou tupla ou DataFrame diretamente
            if isinstance(result, tuple) and len(result) == 2:
                data, metadata = result
            else:
                data = result
                metadata = {}

            if not data.empty:
                # Combinar com dados existentes
                existing_df = self.market_data.get(symbol, {}).get(timeframe)

                if existing_df is not None and not existing_df.empty:
                    combined_df, _ = self._merge_ohlcv_data(
                        symbol, timeframe, existing_df, data
                    )
                    logger.debug(f"Dados combinados: {len(combined_df)} candles totais")
                else:
                    combined_df, _ = self._merge_ohlcv_data(
                        symbol, timeframe, None, data
                    )
                    logger.debug(f"Novos dados carregados: {len(combined_df)} candles")
                self.market_data.setdefault(symbol, {})[timeframe] = combined_df

                # Log metadata
                if metadata.get("cache_hit"):
                    logger.debug("Dados obtidos do cache")
                if metadata.get("exchange_limit_reached"):
                    logger.warning("Limite da exchange atingido durante busca")

            else:
                logger.warning(
                    f"Nenhum dado histórico obtido para {symbol}@{timeframe}"
                )

        except (
            ccxt.NetworkError,
            ccxt.ExchangeError,
            RuntimeError,
            ValueError,
        ) as e:
            logger.error(
                f"Erro ao buscar dados históricos via gerenciador adaptativo: {e}"
            )
            # Fallback para método original
            await self._fetch_ohlcv_history_original(symbol, timeframe, missing)

    async def _fetch_ohlcv_history_original(
        self, symbol: str, timeframe: str, missing: int
    ) -> None:
        """Método original de busca de dados históricos (fallback)."""

        if not self.exchange or missing <= 0:
            return

        try:
            existing_df = self.market_data.get(symbol, {}).get(timeframe)

            timeframe_minutes = timeframe_to_minutes(timeframe)
            timeframe_ms = timeframe_to_milliseconds(timeframe)

            if existing_df is None or existing_df.empty:
                since = int(time.time() * 1000) - missing * timeframe_ms
                if since < 0:
                    since = 0
                first_ts_ms = None
                logger.info(
                    "Buscando historico de %s candles para %s@%s sem cache previo.",
                    missing,
                    symbol,
                    timeframe,
                )
            else:
                first_ts = existing_df["timestamp"].iloc[0]
                if isinstance(first_ts, (pd.Timestamp, datetime)):
                    first_ts_ms = int(pd.Timestamp(first_ts).timestamp() * 1000)
                else:
                    first_ts_ms = int(first_ts)
                since = max(0, first_ts_ms - missing * timeframe_ms)

                throttled_debug(
                    "Buscando historico adicional de %s candles para %s@%s.",
                    missing,
                    symbol,
                    timeframe,
                )

            limit = 720
            if hasattr(self.exchange, "max_candles"):
                try:
                    limit = self.exchange.max_candles(timeframe)
                except (
                    RuntimeError,
                    ValueError,
                ):  # noqa: BLE001 - fallback to default
                    limit = 720

            if missing > limit:
                df_add = await self.exchange.fetch_historical_data(
                    MarketSpec(symbol=symbol, timeframe=timeframe),
                    start_date=datetime.fromtimestamp(since / 1000, tz=timezone.utc),
                    end_date=datetime.now(timezone.utc),
                    use_cache=False,
                )
                if isinstance(df_add, np.ndarray):
                    df_add = pd.DataFrame(
                        df_add,
                        columns=[
                            "timestamp",
                            "open",
                            "high",
                            "low",
                            "close",
                            "volume",
                        ],
                    )
                elif not isinstance(df_add, pd.DataFrame):
                    logger.warning(
                        "Tipo inesperado retornado por fetch_historical_data para %s@%s: %s",
                        symbol,
                        timeframe,
                        type(df_add),
                    )
                    df_add = pd.DataFrame(df_add)
            else:
                raw = await self.exchange.fetch_ohlcv(
                    symbol, timeframe, since=since, limit=missing
                )

                is_empty = False
                if raw is None:
                    is_empty = True
                elif isinstance(raw, pd.DataFrame):
                    is_empty = raw.empty
                elif isinstance(raw, np.ndarray):
                    is_empty = raw.size == 0
                elif isinstance(raw, (list, tuple, Sequence)):
                    is_empty = len(raw) == 0

                if is_empty:
                    logger.warning(
                        "Nenhum dado historico retornado para %s@%s.",
                        symbol,
                        timeframe,
                    )
                    return

                if isinstance(raw, pd.DataFrame):
                    df_add = raw
                else:
                    if isinstance(raw, np.ndarray):
                        tmp = raw
                    else:
                        tmp = list(raw)
                    df_add = pd.DataFrame(
                        tmp,
                        columns=[
                            "timestamp",
                            "open",
                            "high",
                            "low",
                            "close",
                            "volume",
                        ],
                    )
                df_add["timestamp"] = pd.to_datetime(
                    df_add["timestamp"], unit="ms", utc=True
                )

            if df_add is None or df_add.empty:
                logger.warning(
                    "Nenhum dado historico retornado para %s@%s.",
                    symbol,
                    timeframe,
                )
                return

            assert isinstance(df_add, pd.DataFrame), (
                "_fetch_ohlcv_history: tipo inesperado apos conversao:"
                f" {type(df_add)}"
            )

            if first_ts_ms is not None:
                df_add = df_add[
                    df_add["timestamp"]
                    < pd.to_datetime(first_ts_ms, unit="ms", utc=True)
                ]

            combined, _ = self._merge_ohlcv_data(symbol, timeframe, existing_df, df_add)
            assert isinstance(combined, pd.DataFrame), (
                "_fetch_ohlcv_history: resultado do merge nao e DataFrame:"
                f" {type(combined)}"
            )
            self.market_data[symbol][timeframe] = combined
        except (
            OSError,
            RuntimeError,
            ValueError,
        ) as exc:  # noqa: BLE001 - registrar e continuar
            logger.error(
                "Erro ao buscar historico adicional para %s@%s: %s",
                symbol,
                timeframe,
                exc,
                exc_info=True,
            )

    def _create_circuit_from_market_features(
        self, market_features: Dict[str, Any], n_qubits: int
    ) -> Optional[QuantumCircuit]:
        """Delega a criação do circuito ao :class:`TradingQASTCore`."""
        return self.qast_core.create_circuit_from_market_features(
            market_features, n_qubits
        )

    async def _analyze_symbols(self, trace_id: Optional[str] = None) -> Dict[str, Any]:
        """Analisa cada símbolo usando sua estratégia e QAST engine."""

        start_time = time.perf_counter()
        if trace_id:
            logger.info("TraceID: %s - Iniciando análise de símbolos", trace_id)
        else:
            logger.info("Iniciando análise de símbolos")

        # Limpa avisos de volume para o ciclo atual
        self._volume_warning_logged.clear()

        market_features: Dict[str, Any] = (
            {}
        )  # YAA: Inicializar market_features para evitar UnboundLocalError
        # YAA: SEGURANÇA - Garantir que os risk_managers estão inicializados para todos os símbolos
        self._ensure_risk_managers_initialized()

        cycle_processed = 0
        cycle_rejected_risk = 0
        cycle_rejected_confidence = 0

        analysis_results = {}
        for symbol in self.symbols:  # Indentação corrigida
            logger.debug("Analisando %s...", symbol)
            # Indentação corrigida para estar dentro do loop
            logger.debug(f"Iniciando análise para {symbol}")

            # Garantir que temos dados para o timeframe primário
            data_map = self.market_data.get(symbol)
            df = data_map.get(self.primary_timeframe) if data_map else None

            df_is_empty = False
            if isinstance(df, pd.DataFrame):
                df_is_empty = df.empty
            elif isinstance(df, np.ndarray):
                df_is_empty = df.size == 0
            elif isinstance(df, list):
                df_is_empty = len(df) == 0

            if data_map is None or df is None or df_is_empty:
                logger.debug(
                    "Sem dados de mercado para análise de %s ou DataFrame principal vazio/ausente.",
                    symbol,
                )
                logger.warning(
                    f"Dados de mercado insuficientes ou ausentes para {symbol} no timeframe {self.primary_timeframe}. Pulando análise."
                )
                # YAA: Corrigido para checar self.metacognition_executive
                if self.metacognition_executive:  # Era self.metacognition_engine
                    await self._run_metacognition_cycle(symbol, None)
                continue  # Pula para o próximo símbolo se não houver dados

            timeframe_results = {}  # Indentação corrigida para 12 espaços
            primary_tf = self.primary_timeframe  # Indentação corrigida para 12 espaços
            logger.debug("Analisando %s no timeframe principal %s", symbol, primary_tf)

            df = self.market_data[symbol][primary_tf]
            # YAA: Log após obter df de self.market_data
            if isinstance(df, pd.DataFrame) and not df.empty:
                logger.info(
                    f"_analyze_symbols: Para {symbol}@{primary_tf}, DataFrame obtido de self.market_data tem {len(df)} linhas."
                )
                # logger.info(f"_analyze_symbols: {symbol}@{primary_tf} HEAD:\\n{df.head(3).to_string()}") # Opcional, pode ser muito verboso
                # logger.info(f"_analyze_symbols: {symbol}@{primary_tf} TAIL:\\n{df.tail(3).to_string()}") # Opcional
            elif isinstance(df, pd.DataFrame) and df.empty:
                logger.info(
                    f"_analyze_symbols: Para {symbol}@{primary_tf}, DataFrame obtido de self.market_data está VAZIO."
                )
            else:  # df is None
                logger.info(
                    f"_analyze_symbols: Para {symbol}@{primary_tf}, DataFrame obtido de self.market_data é None."
                )

            # Preço atual para decisões
            current_price = (
                df["close"].iloc[-1]
                if isinstance(df, pd.DataFrame) and not df.empty
                else 0
            )

            # YAA: Obter diretivas de modulação do ACE a partir do contexto metacognitivo mais recente
            ace_params_from_metacognition: Optional[Dict[str, Any]] = (
                None  # YAA: Tipo alterado para Dict
            )
            if self._last_metacognitive_ctx and hasattr(
                self._last_metacognitive_ctx, "ace_modulation_parameters"
            ):
                # Assegurar que ace_modulation_parameters é um dict (ou pode ser None)
                raw_params = self._last_metacognitive_ctx.ace_modulation_parameters
                if isinstance(raw_params, dict):
                    ace_params_from_metacognition = raw_params
                elif hasattr(raw_params, "to_dict") and callable(
                    getattr(raw_params, "to_dict")
                ):  # Para dataclasses
                    ace_params_from_metacognition = raw_params.to_dict()

                if ace_params_from_metacognition:
                    logger.info(
                        f"ACE em _analyze_symbols para {symbol} usará diretivas: {ace_params_from_metacognition}"
                    )
                else:
                    logger.debug(
                        f"ACE em _analyze_symbols para {symbol}: sem diretivas de modulação do contexto metacognitivo (ace_modulation_parameters era None ou não conversível para dict)."
                    )
            else:
                logger.debug(
                    f"ACE em _analyze_symbols para {symbol}: sem contexto metacognitivo para obter diretivas de modulação."
                )

            # YAA: Reconfiguração do universo com
            # AdaptiveConsciousnessEvolution
            if len(df) >= 2:
                volatility_metric_raw = df["close"].pct_change().dropna().std()
                if pd.isna(volatility_metric_raw) or volatility_metric_raw == 0:
                    volatility_for_reconfig = 0.05
                else:
                    volatility_for_reconfig = min(
                        1.0, float(volatility_metric_raw) * 30.0
                    )

                if len(df) >= 20:
                    recent_df = df.iloc[-20:]
                    price_returns = recent_df["close"].pct_change().dropna()
                    acf_lag1 = (
                        price_returns.autocorr(lag=1) if len(price_returns) > 1 else 0.0
                    )  # Default to 0.0 if not enough data
                    predictability = abs(acf_lag1) if pd.notna(acf_lag1) else 0.5
                    non_linearity = 1.0 - predictability
                else:
                    non_linearity = 0.5  # Default se não houver dados suficientes
                    logger.debug(
                        f"Dados insuficientes para non_linearity em {symbol}, usando default 0.5"
                    )

                logger.debug(
                    f"Para ACE - {symbol}: Volatility_raw={volatility_metric_raw:.6f}, Volatility_for_reconfig={volatility_for_reconfig:.3f}, Non_linearity={non_linearity:.3f}"
                )

                market_data_for_adaptive_attrs = {
                    "symbol": symbol,
                    "timeframe": primary_tf,
                    "volatility": volatility_for_reconfig,
                    "non_linearity": non_linearity,
                }
                if self.adaptive_consciousness_engine:
                    # YAA: Corrigida a chamada para assess_and_adapt e passando o dicionário de atributos
                    # YAA: Passar também os parâmetros de modulação do contexto
                    # metacognitivo
                    self.adaptive_consciousness_engine.assess_and_adapt(
                        market_obs_for_adaptation=market_data_for_adaptive_attrs,  # Renomeado para clareza
                        ace_modulation_directives=ace_params_from_metacognition,  # YAA: Novo argumento
                    )

            # YAA: Executar Universo QUALIA, gerar assinatura e métricas ANTES da decisão da estratégia
            # Etapa 1: Obter perception_data do QuantumMetricsCalculator (AINDA FAZEMOS ISSO COMO FALLBACK)
            # ESTA LINHA ABAIXO JÁ FOI MOVIDA E CORRIGIDA ANTERIORMENTE:
            # perception_data = self.qmc.encode_market_data(self.market_data)
            # ... (código que calcula perception_data já existe)

            # YAA: Capturar o retorno bruto da geração da assinatura
            # ESTA PARTE PRECISA SER REVISADA PARA USAR O NOVO `market_features`
            # DENTRO DE `perception_data` ou similar para que `QuantumEncodingInterface` o veja.

            current_q_signature_packet: Optional[QuantumSignaturePacket] = (
                None  # Mover a definição para cá
            )

            # ---- Market features for encoders ----
            market_features = {}
            if isinstance(df, pd.DataFrame) and not df.empty:
                price_change_raw = (
                    df["close"].pct_change(periods=1).iloc[-1] if len(df) > 1 else 0.0
                )
                market_features[f"{symbol}_{primary_tf}_price_change"] = (
                    price_change_raw if pd.notna(price_change_raw) else 0.0
                )

                volatility_window = 14
                if len(df) >= volatility_window:
                    volatility_raw = (
                        df["close"]
                        .pct_change()
                        .rolling(window=volatility_window)
                        .std()
                        .iloc[-1]
                    )
                elif len(df) > 1:
                    volatility_raw = df["close"].pct_change().std()
                else:
                    volatility_raw = 0.0
                market_features[f"{symbol}_{primary_tf}_volatility"] = (
                    volatility_raw if pd.notna(volatility_raw) else 0.0
                )

                vol_col = next((c for c in df.columns if c.lower() == "volume"), None)
                vol_key = (symbol, primary_tf)
                if vol_col:
                    volume_avg_window = 20
                    current_volume = df[vol_col].iloc[-1] if len(df) > 0 else 0.0
                    if len(df) >= volume_avg_window:
                        avg_volume = (
                            df[vol_col]
                            .rolling(window=volume_avg_window)
                            .mean()
                            .iloc[-1]
                        )
                    elif len(df) > 0:
                        avg_volume = df[vol_col].mean()
                    else:
                        avg_volume = 0.0

                    if avg_volume <= 0:
                        if not self._volume_warning_logged.get(vol_key):
                            logger.warning(
                                f"Volume médio não disponível para {symbol}@{primary_tf}. Usando valor neutro."
                            )
                            self._volume_warning_logged[vol_key] = True
                        volume_ratio_raw = 1.0
                    else:
                        volume_ratio_raw = current_volume / avg_volume

                    market_features[f"{symbol}_{primary_tf}_volume_ratio"] = (
                        volume_ratio_raw if pd.notna(volume_ratio_raw) else 1.0
                    )
                else:
                    if not self._volume_warning_logged.get(vol_key):
                        logger.warning(
                            f"Coluna 'volume' ausente para {symbol}@{primary_tf}. volume_ratio definido como neutro."
                        )
                        self._volume_warning_logged[vol_key] = True
                    market_features[f"{symbol}_{primary_tf}_volume_ratio"] = 1.0

                if len(df) >= 15:
                    delta = df["close"].diff()
                    gain = delta.where(delta > 0, 0.0)
                    loss = -delta.where(delta < 0, 0.0)
                    avg_gain = gain.rolling(window=14, min_periods=1).mean().iloc[-1]
                    avg_loss = loss.rolling(window=14, min_periods=1).mean().iloc[-1]
                    if avg_loss > 0:
                        rs = avg_gain / avg_loss
                        rsi_value = 100.0 - (100.0 / (1.0 + rs))
                    else:
                        rsi_value = 100.0 if avg_gain > 0 else 50.0
                    market_features[f"{symbol}_{primary_tf}_rsi"] = (
                        rsi_value if pd.notna(rsi_value) else 50.0
                    )
                else:
                    market_features[f"{symbol}_{primary_tf}_rsi"] = 50.0

                # -- Additional 5m timeframe metrics --
                tf_5m = "5m"
                df_5m = self.market_data.get(symbol, {}).get(tf_5m)
                if (
                    tf_5m in self.timeframes
                    and isinstance(df_5m, pd.DataFrame)
                    and not df_5m.empty
                ):
                    if len(df_5m) > 1:
                        pc_5m_raw = df_5m["close"].pct_change().iloc[-1]
                    else:
                        pc_5m_raw = 0.0
                    market_features[f"{symbol}_{tf_5m}_price_change"] = (
                        pc_5m_raw if pd.notna(pc_5m_raw) else 0.0
                    )

                    if len(df_5m) >= volatility_window:
                        vol_5m_raw = (
                            df_5m["close"]
                            .pct_change()
                            .rolling(window=volatility_window)
                            .std()
                            .iloc[-1]
                        )
                    elif len(df_5m) > 1:
                        vol_5m_raw = df_5m["close"].pct_change().std()
                    else:
                        vol_5m_raw = 0.0
                    market_features[f"{symbol}_{tf_5m}_volatility"] = (
                        vol_5m_raw if pd.notna(vol_5m_raw) else 0.0
                    )

                    vol_col_5m = next(
                        (c for c in df_5m.columns if c.lower() == "volume"), None
                    )
                    vol_key_5m = (symbol, tf_5m)
                    if vol_col_5m:
                        current_vol = df_5m[vol_col_5m].iloc[-1]
                        if len(df_5m) >= 20:
                            avg_vol = (
                                df_5m[vol_col_5m].rolling(window=20).mean().iloc[-1]
                            )
                        else:
                            avg_vol = df_5m[vol_col_5m].mean()

                        if avg_vol <= 0:
                            if not self._volume_warning_logged.get(vol_key_5m):
                                logger.warning(
                                    f"Volume médio 5m não disponível para {symbol}. Usando valor neutro."
                                )
                                self._volume_warning_logged[vol_key_5m] = True
                            vr_5m_raw = 1.0
                        else:
                            vr_5m_raw = current_vol / avg_vol
                    else:
                        if not self._volume_warning_logged.get(vol_key_5m):
                            logger.warning(
                                f"Coluna 'volume' ausente em dados 5m para {symbol}. volume_ratio definido como neutro."
                            )
                            self._volume_warning_logged[vol_key_5m] = True
                        vr_5m_raw = 1.0
                    market_features[f"{symbol}_{tf_5m}_volume_ratio"] = (
                        vr_5m_raw if pd.notna(vr_5m_raw) else 1.0
                    )

                    if len(df_5m) >= 15:
                        delta_5m = df_5m["close"].diff()
                        gain_5m = delta_5m.where(delta_5m > 0, 0.0)
                        loss_5m = -delta_5m.where(delta_5m < 0, 0.0)
                        avg_gain_5m = (
                            gain_5m.rolling(window=14, min_periods=1).mean().iloc[-1]
                        )
                        avg_loss_5m = (
                            loss_5m.rolling(window=14, min_periods=1).mean().iloc[-1]
                        )
                        if avg_loss_5m > 0:
                            rs_5m = avg_gain_5m / avg_loss_5m
                            rsi_5m = 100.0 - (100.0 / (1.0 + rs_5m))
                        else:
                            rsi_5m = 100.0 if avg_gain_5m > 0 else 50.0
                    else:
                        rsi_5m = 50.0
                    market_features[f"{symbol}_{tf_5m}_rsi"] = (
                        rsi_5m if pd.notna(rsi_5m) else 50.0
                    )

                logger.info(
                    f"Market features populadas para {symbol}: {list(market_features.keys())}"
                )
                logger.debug(
                    f"Detalhes das market features para {symbol}: {json.dumps({k: (f'{v:.4f}' if isinstance(v, float) else v) for k, v in market_features.items()}, default=str)}"
                )

            # Obter/Criar perception_data que INCLUI o market_features que acabamos de calcular.
            if self.qmc:
                # O `market_data` aqui é o dict de DataFrames.
                # O `market_features` é o dict de features escalares que calculamos.
                perception_data = self.qmc.encode_market_data(
                    self.market_data, additional_features=market_features
                )  # YAA: Passando additional_features
            else:
                # Fallback se QMC não existir: usar market_features diretamente como base para perception_data["metrics_flat"]
                # Isso é um cenário de emergência, pois o QMC é central para a codificação.
                perception_data = {
                    "timestamp": datetime.now(timezone.utc).timestamp(),
                    "metrics": {},
                    "metrics_flat": market_features.copy(),
                    "price_changes": [],
                    "patterns": [],
                    "raw_market_data_refs": (
                        list(self.market_data.keys()) if self.market_data else []
                    ),
                }
                logger.warning(
                    "QMC não inicializado. Usando market_features diretamente para perception_data (funcionalidade limitada)."
                )

            valid_circuit_for_universe = None
            initial_market_circuit_from_qmc = None
            if (
                self.qmc
            ):  # Se QMC existe, ele tentará criar o circuito a partir do perception_data que agora contém as features
                initial_market_circuit_from_qmc = self.qmc._create_encoded_market_input(
                    perception_data
                )

            if initial_market_circuit_from_qmc is not None:
                logger.info(
                    f"QMC gerou circuito para {symbol}: Qubits={initial_market_circuit_from_qmc.num_qubits}, Profundidade={initial_market_circuit_from_qmc.depth()}."
                )
                if initial_market_circuit_from_qmc.depth() > 0:
                    expected_qubits = (
                        self.qualia_universe.n_qubits
                        if self.qualia_universe
                        else (self.qmc.n_qubits if self.qmc else 2)
                    )
                    if initial_market_circuit_from_qmc.num_qubits == expected_qubits:
                        valid_circuit_for_universe = initial_market_circuit_from_qmc
                        logger.info(
                            f"Circuito do QMC para {symbol} validado (Qubits: {initial_market_circuit_from_qmc.num_qubits}, Profundidade: {initial_market_circuit_from_qmc.depth()}) definido como circuito base/fallback."
                        )
                    else:
                        logger.warning(
                            f"DISCORDÂNCIA DE QUBITS para {symbol}: Circuito QMC tem {initial_market_circuit_from_qmc.num_qubits} qubits, Universo espera {expected_qubits}. Circuito do QMC não será usado."
                        )
                else:
                    logger.warning(
                        f"Circuito do QMC para {symbol} tem profundidade 0. Circuito do QMC não será usado."
                    )
            if self.qualia_universe:
                # A função _create_circuit_from_market_features espera um dict 'market_features'.
                # Ela foi projetada para usar 'price_change_5m', 'volatility_5m', etc.
                # Precisamos decidir se _create_circuit_from_market_features será atualizada
                # ou se passamos um 'market_features' adaptado para ela.
                # Por agora, vou assumir que _create_circuit_from_market_features
                # também deve usar as features baseadas no primary_tf.
                # No entanto, _create_circuit_from_market_features é usada para o "direct_feature_circuit"
                # que é uma alternativa ao circuito do QMC. O QMC (QuantumEncodingInterface)
                # é o que está reclamando das chaves ausentes.

                # O critical é que `perception_data` que vai para `self.quantum_encoding_interface.encode_market_state`
                # precisa ter `metrics_flat` com as chaves corretas.
                # `perception_data` é construído por `self.qmc.encode_market_data(self.market_data)`
                # E `self.qmc.encode_market_data` usa os encoders configurados, que por sua vez
                # esperam as chaves f"{symbol}_{timeframe}_feature".
                # Então, o `market_features` que construímos aqui precisa estar disponível para o QMC.

                # A forma como QMC obtém os dados é através de perception_data que ele mesmo cria.
                # QMC.encode_market_data(self.market_data) -> chama QuantumEncodingInterface.encode_market_state(perception_data, ...)
                # E QuantumEncodingInterface lê de perception_data["metrics_flat"] ou perception_data diretamente.
                # Então, o `market_features` que acabamos de calcular precisa ir para `perception_data["metrics_flat"]`.

                # Onde `perception_data` é criado e preenchido?
                # Linha 1735: perception_data = self.qmc.encode_market_data(self.market_data)
                # Isso significa que `qmc.encode_market_data` deve ser o responsável por ter essas features.
                # Mas os logs de erro vêm de dentro de `QuantumEncodingInterface` que é chamado por `qmc._create_encoded_market_input`
                # que por sua vez é chamado por `qmc.encode_market_data`.
                # O `QuantumEncodingInterface` lê as features de `perception_data` (que é o argumento para `encode_market_state`).

                # Vamos garantir que o `market_features` calculado seja fundido no `perception_data`
                # que é passado para `self.quantum_encoding_interface.encode_market_state`.
                # O `QuantumMetricsCalculator.encode_market_data` (que chama `_create_encoded_market_input`)
                # precisa ter acesso a estas features.
                # A solução mais direta é popular `metrics_flat` ANTES de chamar o QMC,
                # ou garantir que o QMC saiba calcular/acessar estas features.

                # A estrutura atual:
                # 1. _analyze_symbols calcula `market_features` (AGORA CORRIGIDO).
                # 2. _analyze_symbols chama `self.qmc.encode_market_data(self.market_data)` que retorna `perception_data`.
                #    Esta função QMC DEVE usar as `market_features` para os encoders.
                #    Internamente, `qmc.encode_market_data` chama `_create_encoded_market_input`, que chama
                #    `quantum_encoding_interface.encode_market_state(perception_data, ...)`.
                #    O `perception_data` que `encode_market_state` recebe é o que foi construído DENTRO de `qmc.encode_market_data`.

                # PRECISAMOS que as `market_features` calculadas em `_analyze_symbols` sejam
                # acessíveis por `QuantumEncodingInterface` quando ela busca por `dk` em `metrics_flat` ou `perception_data`.

                # Uma forma é modificar `qmc.encode_market_data` para aceitar `market_features` e fundi-las.
                # Outra forma é passar `market_features` para o `trade_context` e garantir que o QMC ou
                # a `QuantumEncodingInterface` possam acessá-las de lá se necessário.
                # A `QuantumEncodingInterface` já olha em `perception_data.get("metrics_flat", {})`
                # e `perception_data` diretamente.

                # O `perception_data` é o que o `qmc.encode_market_data` retorna.
                # Se `qmc.encode_market_data` é quem prepara os dados para os encoders,
                # então ele precisa ter acesso a essas features.
                # Atualmente, `qmc.encode_market_data` apenas recebe `self.market_data`.

                # SOLUÇÃO: O QMC precisa ser capaz de calcular essas features internamente ou recebê-las.
                # Como já calculamos em `_analyze_symbols`, vamos tentar passar para o QMC.
                # Alterar a chamada:
                #   perception_data = self.qmc.encode_market_data(self.market_data)
                # para:
                #   perception_data = self.qmc.encode_market_data(self.market_data, precomputed_features=market_features)
                # E modificar `qmc.encode_market_data` para usar `precomputed_features`.

                # No momento, vamos apenas garantir que `market_features` está correto.
                # O `direct_feature_circuit` usa `market_features`.
                direct_feature_circuit = self._create_circuit_from_market_features(
                    market_features, self.qualia_universe.n_qubits
                )

                if direct_feature_circuit:
                    logger.info(
                        f"Circuito criado diretamente de market_features para {symbol}. Qubits: {direct_feature_circuit.num_qubits}, Profundidade: {direct_feature_circuit.depth()}"
                    )
                    if (
                        direct_feature_circuit.num_qubits
                        == self.qualia_universe.n_qubits
                    ):
                        valid_circuit_for_universe = (
                            direct_feature_circuit  # Substituição!
                        )
                        logger.info(
                            f"SUBSTITUINDO: Usando circuito derivado diretamente de market_features (Qubits: {valid_circuit_for_universe.num_qubits}) para o universo."
                        )
                    else:
                        logger.warning(
                            f"Circuito de market_features ({direct_feature_circuit.num_qubits} qubits) incompatível com n_qubits do universo ({self.qualia_universe.n_qubits}). Usando circuito do QMC (se disponível e válido)."
                        )
                else:
                    logger.info(
                        f"Não foi possível criar circuito de market_features para {symbol}. Usando circuito do QMC (se disponível e válido)."
                    )
            else:
                logger.warning(
                    "Universo QUALIA não inicializado, não é possível criar ou usar circuito de features diretas nem executar o universo."
                )

            # Etapa 3: Executar o universo com o circuito selecionado (valid_circuit_for_universe)
            universe_run_results = (
                None  # Inicializar para o caso do universo não ser executado
            )
            if not self.qualia_universe:
                logger.error(
                    f"QUALIAQuantumUniverse não inicializado antes de tentar executar para {symbol}. Pulando execução do universo."
                )
                universe_run_results = {
                    "error": "QUALIAQuantumUniverse not initialized"
                }
            elif valid_circuit_for_universe is None:
                logger.warning(
                    f"Nenhum circuito válido (nem do QMC, nem de features diretas) para {symbol}. Universo não será executado."
                )
                universe_run_results = {
                    "error": "No valid circuit for universe execution"
                }
            else:
                logger.debug(
                    f"Executando QUALIAQuantumUniverse para {symbol} com circuito final: {type(valid_circuit_for_universe)}, Qubits: {valid_circuit_for_universe.num_qubits}, Profundidade: {valid_circuit_for_universe.depth()}"
                )
                universe_run_results = self.qualia_universe.run(
                    steps=1, initial_encoded_circuit=valid_circuit_for_universe
                )
                # Mover aqui para garantir que run foi chamado
                await self.qualia_universe.update_last_statevector()

            # YAA: Capturar o statevector imediatamente após a atualização e ANTES de qualquer possível reconfiguração pelo ACE
            sv_for_sig: Optional[Statevector] = None
            if self.qualia_universe and self.qualia_universe.current_sv:
                sv_for_sig = copy.deepcopy(self.qualia_universe.current_sv)
                logger.debug(
                    f"Statevector copiado para assinatura para {symbol}. Qubits: {sv_for_sig.num_qubits if sv_for_sig else 'N/A'}"
                )
            else:
                logger.warning(
                    f"Não foi possível copiar o statevector para {symbol} (universo ou current_sv ausente após run/update)."
                )

            pnl_feedback_for_metacognition = None
            if self.trade_history:
                for trade in reversed(self.trade_history):
                    if trade.get("symbol") == symbol and trade.get(
                        "status", ""
                    ).startswith("closed"):
                        pnl_feedback_for_metacognition = trade.get("pnl")
                        logger.debug(
                            f"PnL do último trade fechado para {symbol} ({trade.get('order_id')}): {pnl_feedback_for_metacognition} será usado para metacognição."
                        )
                        break

            current_q_signature: Optional[List[float]] = None
            current_q_metrics: Optional[Dict[str, Any]] = None
            # YAA: Adicionar variável para per_encoder_metrics
            current_per_encoder_metrics: Optional[Dict[str, Dict[str, float]]] = None

            if universe_run_results and "error" not in universe_run_results:
                # YAA: Capturar o retorno bruto da geração da assinatura, passando o statevector copiado
                raw_sig_data = await self.qast_core.generate_quantum_signature(
                    self.qualia_universe, statevector_override=sv_for_sig
                )
                logger.debug(
                    f"Dados brutos da assinatura quântica para {symbol}: {type(raw_sig_data)}, Conteúdo (parcial): {str(raw_sig_data)[:200]}"
                )

                # Tentar extrair métricas do universo se disponíveis
                if (
                    hasattr(self.qualia_universe, "metrics")
                    and self.qualia_universe.metrics is not None
                    and hasattr(self.qualia_universe.metrics, "get_metrics_dict")
                    and callable(
                        getattr(self.qualia_universe.metrics, "get_metrics_dict")
                    )
                ):
                    current_q_metrics = self.qast_core.collect_quantum_metrics(
                        self.qualia_universe
                    )
                elif (
                    isinstance(universe_run_results, tuple)
                    and len(universe_run_results) > 1
                    and isinstance(universe_run_results[1], dict)
                ):
                    current_q_metrics = universe_run_results[1]
                    # YAA: Extrair run_counts para usar em _extract_encoder_metrics_from_counts
                    run_counts = (
                        universe_run_results[0]
                        if isinstance(universe_run_results[0], dict)
                        else None
                    )
                    if (
                        run_counts
                        and self.qmc
                        and self.qualia_universe
                        and self.qualia_universe.n_qubits is not None
                    ):
                        logger.debug(
                            f"Tentando extrair métricas por encoder. QMC: {self.qmc}, Mapping: {hasattr(self.qmc, '_last_encoder_qubit_mapping')}, Universe n_qubits: {self.qualia_universe.n_qubits}"
                        )
                        if (
                            hasattr(self.qmc, "_last_encoder_qubit_mapping")
                            and self.qmc._last_encoder_qubit_mapping
                        ):
                            current_per_encoder_metrics = self.qmc._extract_encoder_metrics_from_counts(
                                raw_counts=run_counts,
                                encoder_qubit_mapping=self.qmc._last_encoder_qubit_mapping,
                                num_total_qubits_in_circuit=self.qualia_universe.n_qubits,
                            )
                            if current_per_encoder_metrics:
                                logger.info(
                                    f"Métricas por encoder extraídas em _analyze_symbols: {list(current_per_encoder_metrics.keys())}"
                                )
                                logger.debug(
                                    f"Detalhes das métricas por encoder em _analyze_symbols: {json.dumps(current_per_encoder_metrics, indent=2)}"
                                )
                            else:
                                logger.warning(
                                    "Nenhuma métrica por encoder foi extraída por _extract_encoder_metrics_from_counts em _analyze_symbols (retornou vazio/None)."
                                )
                        else:
                            logger.warning(
                                "Não foi possível extrair métricas por encoder: _last_encoder_qubit_mapping não disponível ou vazio no QMC."
                            )
                    else:
                        logger.warning(
                            f"Não foi possível extrair métricas por encoder: run_counts não disponível, QMC não inicializado, universo não disponível ou n_qubits não definido. Counts: {run_counts is not None}, QMC: {self.qmc is not None}, Universe: {self.qualia_universe is not None}, NQubits: {self.qualia_universe.n_qubits if self.qualia_universe else 'N/A'}"
                        )

                else:
                    current_q_metrics = {}
                logger.debug(
                    f"Métricas Quânticas para {symbol} (usadas no QSP): {current_q_metrics}"
                )

                if self.qualia_universe and current_q_metrics:
                    ent_hist = current_q_metrics.get("quantum_entropy")
                    if isinstance(ent_hist, list) and len(ent_hist) >= 2:
                        delta_h = float(ent_hist[-1]) - float(ent_hist[-2])
                        self.qualia_universe.update_entropy_delta(delta_h)

                # YAA: Construir o QuantumSignaturePacket corretamente
                current_q_signature_packet: Optional[QuantumSignaturePacket] = None
                extracted_vector: Optional[List[float]] = None

                if isinstance(raw_sig_data, list) and all(
                    isinstance(item, float) for item in raw_sig_data
                ):
                    extracted_vector = raw_sig_data
                    logger.info(
                        f"Assinatura quântica bruta para {symbol} é uma List[float]."
                    )
                elif isinstance(raw_sig_data, dict):
                    if (
                        "vector" in raw_sig_data
                        and isinstance(raw_sig_data["vector"], list)
                        and all(
                            isinstance(item, float) for item in raw_sig_data["vector"]
                        )
                    ):
                        extracted_vector = raw_sig_data["vector"]
                        # Se o raw_sig_data for um dict que já se parece com um QSP, podemos pegar as métricas dele também.
                        # Caso contrário, usamos current_q_metrics.
                        current_q_metrics = raw_sig_data.get(
                            "metrics", current_q_metrics or {}
                        )
                        logger.info(
                            f"Assinatura quântica bruta para {symbol} é um Dict, vetor extraído. Métricas usadas: {current_q_metrics}"
                        )
                    else:
                        logger.warning(
                            f"Assinatura quântica bruta para {symbol} é um Dict, mas o campo 'vector' está ausente ou não é List[float]. Conteúdo: {raw_sig_data}"
                        )
                elif (
                    hasattr(raw_sig_data, "vector")
                    and isinstance(raw_sig_data.vector, list)
                    and all(isinstance(item, float) for item in raw_sig_data.vector)
                ):
                    # Caso seja um objeto com atributo 'vector' (como um QSP já formado)
                    extracted_vector = raw_sig_data.vector
                    current_q_metrics = getattr(
                        raw_sig_data, "metrics", current_q_metrics or {}
                    )
                    logger.info(
                        f"Assinatura quântica bruta para {symbol} é um objeto com atributo 'vector', vetor extraído."
                    )

                if extracted_vector is not None:
                    try:
                        source_details = (
                            copy.deepcopy(raw_sig_data.source_details)
                            if hasattr(raw_sig_data, "source_details")
                            and raw_sig_data.source_details
                            else {
                                "symbol": symbol,
                                "timeframe": primary_tf,
                                "type": "universe_direct_run_legacy_raw_sig",
                                "n_qubits_estimation_from_vector_len": (
                                    int(np.log2(len(extracted_vector) / 2))
                                    if extracted_vector
                                    and len(extracted_vector) > 0
                                    and len(extracted_vector) % 2 == 0
                                    else None
                                ),
                            }
                        )

                        # Garantir que vector_type esteja presente
                        source_details.setdefault(
                            "vector_type",
                            VectorType.AMPLITUDE_VECTOR_INTERLEAVED_FLOAT,
                        )

                        # Garantir que n_qubits esteja definido
                        if source_details.get("n_qubits") is None:
                            n_qubits_val = None
                            if (
                                self.qualia_universe
                                and getattr(self.qualia_universe, "n_qubits", None)
                                is not None
                            ):
                                n_qubits_val = int(self.qualia_universe.n_qubits)
                            elif extracted_vector and len(extracted_vector) % 2 == 0:
                                n_qubits_val = int(np.log2(len(extracted_vector) / 2))
                            if n_qubits_val is not None:
                                source_details["n_qubits"] = n_qubits_val

                        current_q_signature_packet = QuantumSignaturePacket(
                            vector=extracted_vector,
                            metrics=current_q_metrics or {},
                            per_encoder_metrics=current_per_encoder_metrics or {},
                            timestamp=datetime.now(timezone.utc).timestamp(),
                            source_details=source_details,
                        )
                        logger.info(
                            f"QuantumSignaturePacket criado com sucesso para {symbol} em _analyze_symbols. ID: {current_q_signature_packet.id[:8]}"
                        )

                        # YAA: Sanity check para a dimensão do vetor da assinatura
                        if (
                            current_q_signature_packet
                            and current_q_signature_packet.source_details
                            and current_q_signature_packet.vector is not None
                        ):
                            n_qubits_from_source = (
                                current_q_signature_packet.source_details.get(
                                    "n_qubits"
                                )
                            )
                            if n_qubits_from_source is not None:
                                try:
                                    # Garantir que n_qubits_from_source é um inteiro antes de usar em 2**n
                                    num_q = int(n_qubits_from_source)
                                    expected_len = 2 * (2**num_q)
                                    actual_len = len(current_q_signature_packet.vector)
                                    assert (
                                        actual_len == expected_len
                                    ), f"[DIM-MISMATCH] Para {symbol}, QSP ID {current_q_signature_packet.id[:8]}. Assinatura com n_qubits={num_q} deveria ter len={expected_len}, mas obteve {actual_len}. Source: {current_q_signature_packet.source_details.get('statevector_source', 'N/A')}"
                                    logger.debug(
                                        f"[DIM-OK] Assinatura {current_q_signature_packet.id[:8]} para {symbol}: "
                                        f"n_qubits={num_q} (de source_details) -> len(vector)={actual_len}. Source: {current_q_signature_packet.source_details.get('statevector_source', 'N/A')}"
                                    )
                                except ValueError:
                                    logger.error(
                                        f"[DIM-CHECK-FAIL] Para {symbol}, QSP ID {current_q_signature_packet.id[:8]}. n_qubits em source_details ('{n_qubits_from_source}') não é um inteiro válido."
                                    )
                            else:
                                logger.warning(
                                    f"[DIM-CHECK-WARN] Para {symbol}, QSP ID {current_q_signature_packet.id[:8]}. 'n_qubits' não encontrado em source_details. Não foi possível verificar a dimensão do vetor. Detalhes da Fonte: {current_q_signature_packet.source_details}"
                                )
                        else:
                            logger.warning(
                                f"[DIM-CHECK-SKIP] Para {symbol}. Não foi possível realizar o sanity check da dimensão: QSP, source_details ou vector ausente após a criação."
                            )

                        # YAA: ARMAZENAR O PACOTE NO ATRIBUTO DA CLASSE
                        self._current_quantum_signature_packets[symbol] = (
                            current_q_signature_packet
                        )
                    except (ValueError, TypeError) as e_qsp:
                        logger.exception(
                            "Erro ao criar QuantumSignaturePacket para %s com vetor extraído: %s",
                            symbol,
                            e_qsp,
                        )
                        current_q_signature_packet = (
                            None  # Garante que seja None em caso de falha
                        )
                else:
                    logger.warning(
                        f"Não foi possível extrair um vetor List[float] da assinatura quântica bruta para {symbol}. current_q_signature_packet será None."
                    )
                    current_q_signature_packet = None

                retrieved_similar_patterns = []
                if self.qpm_memory and current_q_signature_packet is not None:
                    try:
                        retrieved_similar_patterns = (
                            self.qpm_memory.recall_similar_patterns(
                                current_q_signature_packet.vector, top_n=5
                            )
                        )
                        for p in retrieved_similar_patterns:
                            ts_val = p.get("pattern", {}).get("timestamp")
                            if ts_val is not None:
                                ts_fmt = datetime.fromtimestamp(
                                    ts_val, timezone.utc
                                ).isoformat()
                                logger.info(
                                    "Padrão de mercado similar ao evento %s detectado com similaridade de %.2f%%",
                                    ts_fmt,
                                    p.get("similarity_score", 0.0) * 100,
                                )
                    except (RuntimeError, ValueError) as e_qpm_retrieve:
                        logger.exception(
                            "Erro ao recuperar padrões da QPM para %s: %s",
                            symbol,
                            e_qpm_retrieve,
                        )
            else:
                if universe_run_results and "error" in universe_run_results:
                    logger.error(
                        f"Erro ao executar o universo para {symbol}: {universe_run_results.get('error')}"
                    )
                else:
                    logger.error(
                        f"Resultado inesperado da execução do universo para {symbol}: {universe_run_results}"
                    )
                retrieved_similar_patterns = []

            market_snapshot_for_qpm = {}
            if isinstance(df, pd.DataFrame) and not df.empty:
                market_snapshot_for_qpm = df.iloc[-1:].to_dict(orient="records")[0]

            decision_context_for_qpm = {
                "strategy_name": (
                    self.strategies[symbol].__class__.__name__
                    if symbol in self.strategies
                    else "UnknownStrategy"
                ),
                "primary_timeframe": primary_tf,
                "cycle_count": self.cycle_count,
            }

            if self.current_metacognitive_context:
                ace_params_from_metacognition = (
                    self.current_metacognitive_context.ace_modulation_parameters
                )

            current_price_for_analysis = (
                df["close"].iloc[-1]
                if isinstance(df, pd.DataFrame) and not df.empty
                else 0.0
            )

            current_bid_ask = self.current_tickers.get(symbol, {})
            current_bid = current_bid_ask.get("bid")
            current_ask = current_bid_ask.get("ask")

            trade_context = TradeContext(
                symbol=symbol,
                timeframe=primary_tf,
                ohlcv=df,  # Mantido para a estratégia poder ter acesso completo ao DF
                current_price=current_price_for_analysis,  # Preço para análise da estratégia
                timestamp=datetime.now(timezone.utc),
                wallet_state=self.wallet_state,
                liquidity=market_features.get("liquidity_estimate", 0.5),
                volatility=market_features.get(
                    f"{symbol}_{primary_tf}_volatility",
                    market_features.get("volatility_5m", 0.01),
                ),
                strategy_metrics=market_features,
                quantum_metrics=(
                    current_q_signature_packet.metrics
                    if current_q_signature_packet
                    else {}  # Usar métricas do pacote
                ),
                market_state="uncertain",
                risk_mode=self.risk_profile,
                current_bid=(float(current_bid) if current_bid is not None else None),
                current_ask=(float(current_ask) if current_ask is not None else None),
                metadata={"trace_id": trace_id} if trace_id else {},
            )

            decision: ScalpingDecision
            if symbol in self.strategies:
                strategy_obj = self.strategies[symbol]
                try:
                    decision_dict = strategy_obj.analyze_market(
                        market_data=df,
                        quantum_metrics=(
                            current_q_signature_packet.metrics
                            if current_q_signature_packet
                            else {}
                        ),
                        trading_context=trade_context,
                        similar_past_patterns=retrieved_similar_patterns,
                    )
                except ValueError:
                    if (
                        hasattr(strategy_obj, "initialized")
                        and not strategy_obj.initialized
                    ):
                        logger.warning(
                            f"Estratégia para {symbol} não inicializada. Tentando reinitializar."
                        )
                        if hasattr(strategy_obj, "initialize"):
                            try:
                                strategy_obj.initialize(strategy_obj.context)
                                logger.info(
                                    f"Estratégia para {symbol} reinitializada com sucesso. Reexecutando análise."
                                )
                                decision_dict = strategy_obj.analyze_market(
                                    market_data=df,
                                    quantum_metrics=(
                                        current_q_signature_packet.metrics
                                        if current_q_signature_packet
                                        else {}
                                    ),
                                    trading_context=trade_context,
                                    similar_past_patterns=retrieved_similar_patterns,
                                )
                            except (RuntimeError, ValueError) as init_exc:
                                logger.exception(
                                    "Falha ao reinitializar estratégia de %s: %s",
                                    symbol,
                                    init_exc,
                                )
                                raise
                        else:
                            logger.error(
                                f"Estratégia para {symbol} não possui método initialize. Reinitialização impossível."
                            )
                            raise
                    else:
                        raise

                class TempDecision:  # Mantido para compatibilidade
                    def __init__(self, data):
                        self.signal = data.get("signal", "HOLD")
                        self.confidence = data.get("confidence", 0.0)
                        self.stop_loss = data.get("stop_loss")
                        self.take_profit = data.get("take_profit")
                        self.reasons = data.get("reasons", [])

                decision = TempDecision(decision_dict)
            else:
                logger.warning(
                    f"Nenhuma estratégia encontrada para {symbol}. Pulando decisão de trading."
                )

                class TempDecisionHold:
                    signal = "HOLD"
                    confidence = 0.0
                    stop_loss = None
                    take_profit = None
                    reasons = []

                decision = TempDecisionHold()

            timeframe_results[primary_tf] = {
                "decision": decision.signal,
                "price": current_price_for_analysis,  # Logar o preço de análise
                "stop_loss": decision.stop_loss,
                "take_profit": decision.take_profit,
                "confidence": decision.confidence,
            }
            logger.info(
                f"Decisão para {symbol}@{primary_tf}: {decision.signal} @ {current_price_for_analysis:.2f} (SL: {decision.stop_loss}, TP: {decision.take_profit}, Conf: {decision.confidence:.2f})"
            )

            # Determinar o preço de gatilho para execução
            execution_trigger_price = (
                current_price_for_analysis  # Default para simulação
            )
            if self.mode == "live":
                if decision.signal == "BUY":
                    if current_ask is not None:
                        execution_trigger_price = float(current_ask)
                        logger.info(
                            f"Modo LIVE: Usando ASK price {execution_trigger_price} para BUY de {symbol}."
                        )
                    else:
                        logger.warning(
                            f"Modo LIVE: ASK price indisponível para BUY de {symbol}. Usando close price {current_price_for_analysis} como fallback."
                        )
                elif (
                    decision.signal == "SELL"
                ):  # Aplica-se a abrir Venda ou fechar Compra
                    if current_bid is not None:
                        execution_trigger_price = float(current_bid)
                        logger.info(
                            f"Modo LIVE: Usando BID price {execution_trigger_price} para SELL de {symbol}."
                        )
                    else:
                        logger.warning(
                            f"Modo LIVE: BID price indisponível para SELL de {symbol}. Usando close price {current_price_for_analysis} como fallback."
                        )
                elif decision.signal == "CLOSE" and self.open_positions.get(symbol):
                    # Para fechar uma posição existente via sinal CLOSE da estratégia
                    # Corrigido para suportar múltiplas posições por símbolo.
                    # Seleciona o lado da primeira posição aberta como referência
                    first_open_position = self.open_positions[symbol][0]
                    active_pos_side = first_open_position.side
                    if active_pos_side == "buy":  # Fechando uma compra (vendendo)
                        if current_bid is not None:
                            execution_trigger_price = float(current_bid)
                            logger.info(
                                f"Modo LIVE: Usando BID price {execution_trigger_price} para fechar BUY de {symbol}."
                            )
                        else:
                            logger.warning(
                                f"Modo LIVE: BID price indisponível para fechar BUY de {symbol}. Usando close price {current_price_for_analysis} como fallback."
                            )
                    elif active_pos_side == "sell":  # Fechando uma venda (comprando)
                        if current_ask is not None:
                            execution_trigger_price = float(current_ask)
                            logger.info(
                                f"Modo LIVE: Usando ASK price {execution_trigger_price} para fechar SELL de {symbol}."
                            )
                        else:
                            logger.warning(
                                f"Modo LIVE: ASK price indisponível para fechar SELL de {symbol}. Usando close price {current_price_for_analysis} como fallback."
                            )

            # Construção do metacognition_input movida para antes do processamento
            if not self.disable_metacognition and self.metacognition_executive:
                metacognition_input_data = {
                    "symbol": symbol,
                    "decision": (
                        asdict(decision)
                        if hasattr(decision, "__dataclass_fields__")
                        else {
                            "signal": getattr(
                                decision, "signal", "HOLD"
                            ),  # Usar getattr para segurança
                            "confidence": getattr(decision, "confidence", 0.0),
                            "stop_loss": getattr(decision, "stop_loss", None),
                            "take_profit": getattr(decision, "take_profit", None),
                            "reasons": getattr(decision, "reasons", []),
                        }
                    ),
                    "market_snapshot": market_snapshot_for_qpm,
                    "current_q_signature_packet": current_q_signature_packet,
                    "quantum_metrics": (  # Adicionado para consistência, embora possa ser derivado do pacote
                        current_q_signature_packet.metrics
                        if current_q_signature_packet
                        and hasattr(current_q_signature_packet, "metrics")
                        else {}
                    ),
                    "current_pnl_overall": self.wallet_state["total_pnl"],
                    "pnl_from_last_trade": pnl_feedback_for_metacognition,
                    "market_regime": trade_context.market_state,
                    # Adicionar outros dados relevantes do trade_context se necessário
                    "current_price_for_analysis": current_price_for_analysis,  # Adicionado
                    "current_bid": current_bid,  # Adicionado
                    "current_ask": current_ask,  # Adicionado
                    "wallet_state_full": self.wallet_state,  # Adicionado estado completo da carteira
                    "open_positions_full": self.open_positions,  # Adicionado posições abertas completas
                }

                await self._run_metacognition_cycle(symbol, metacognition_input_data)

            if decision.signal in {"BUY", "SELL"}:
                cycle_processed += 1
                if decision.confidence < self.trade_decision_confidence_threshold:
                    cycle_rejected_confidence += 1
                    logger.info(
                        f"Sinal {decision.signal} para {symbol} descartado por confiança {decision.confidence:.2f} < {self.trade_decision_confidence_threshold}"
                    )
                    continue
                async with self._pos_lock:
                    curr_pos = len(self.open_positions.get(symbol, []))
                can_open, reason = self.risk_managers[symbol].can_open_new_position(
                    curr_pos,
                    trace_id=trace_id,
                )
                if not can_open:
                    cycle_rejected_risk += 1
                    logger.info(
                        f"Sinal {decision.signal} para {symbol} rejeitado pelo RiskManager: {reason}"
                    )
                    continue
                size_details = self.risk_managers[symbol].calculate_position_size(
                    current_price=execution_trigger_price,
                    stop_loss_price=decision.stop_loss,
                    confidence=decision.confidence,
                    symbol=symbol,
                    side=decision.signal.lower(),
                    lambda_factor=getattr(
                        self.qualia_universe, "lambda_factor_multiplier", 1.0
                    ),
                    trace_id=trace_id,
                )
                if size_details.get("quantity", 0.0) <= 0:
                    cycle_rejected_risk += 1
                    logger.info(
                        f"Sinal {decision.signal} para {symbol} rejeitado por tamanho de posição: {size_details.get('reason', 'size_zero')}"
                    )
                    continue

            await self._process_trading_decision(
                symbol,
                decision,
                execution_trigger_price,
                current_q_signature_packet,
                market_snapshot_for_qpm,
                decision_context_for_qpm,
                trace_id=trace_id,
            )

            analysis_results[symbol] = timeframe_results

        self.signal_counters["processed"] += cycle_processed
        self.signal_counters["rejected_risk"] += cycle_rejected_risk
        self.signal_counters["rejected_confidence"] += cycle_rejected_confidence

        logger.info(
            "Ciclo analisado: %d sinais, %d rejeitados por risco, %d por confiança",
            cycle_processed,
            cycle_rejected_risk,
            cycle_rejected_confidence,
        )
        elapsed_ms = (time.perf_counter() - start_time) * 1000
        if self.statsd:
            tags = [f"trace_id:{trace_id}"] if trace_id else None
            self.statsd.timing("trading.decision_ms", elapsed_ms, tags=tags)
            self.statsd.increment("trading.decision_count", tags=tags)
        record_metric("trading.decision_ms", elapsed_ms)
        record_metric("trading.decision_count", cycle_processed)
        return analysis_results

    async def analyze_after_ticker_update(self, max_age: float = 5.0) -> Dict[str, Any]:
        """Wait for a fresh ticker update then run :meth:`_analyze_symbols`."""

        if not self.exchange:
            return await self._analyze_symbols(trace_id=None)

        start = time.time()
        while True:
            now = time.time()
            all_fresh = True
            for sym in self.symbols:
                try:
                    norm = normalize_symbol(sym, self.exchange.exchange)
                except ValueError:
                    norm = sym
                cached = getattr(self.exchange, "ticker_cache", {}).get(norm)
                if not cached or now - cached[1] > max_age:
                    all_fresh = False
                    break
            if all_fresh or now - start > max_age * 5:
                break
            await asyncio.sleep(0.1)

        return await self._analyze_symbols(trace_id=None)

    def _parse_decision_data(self, decision_data_raw: Any) -> Dict[str, Any]:
        """Return decision data in dictionary form.

        Parameters
        ----------
        decision_data_raw
            Valor bruto recebido de ``metacognition_input["decision"]``.

        Returns
        -------
        Dict[str, Any]
            Estrutura padronizada contendo ``signal`` e parâmetros relacionados.
        """

        if decision_data_raw is None:
            return {
                "signal": "HOLD",
                "confidence": 0.0,
                "stop_loss": None,
                "take_profit": None,
                "reasons": [],
            }

        if isinstance(decision_data_raw, dict):
            return decision_data_raw

        if hasattr(decision_data_raw, "__dataclass_fields__"):
            return asdict(decision_data_raw)

        return {
            "signal": getattr(decision_data_raw, "signal", "HOLD"),
            "confidence": getattr(decision_data_raw, "confidence", 0.0),
            "stop_loss": getattr(decision_data_raw, "stop_loss", None),
            "take_profit": getattr(decision_data_raw, "take_profit", None),
            "reasons": getattr(decision_data_raw, "reasons", []),
        }

    def _build_decision_context(
        self,
        symbol: str,
        decision_data: Dict[str, Any],
        metacognition_input: Optional[Dict[str, Any]],
    ) -> DecisionContext:
        """Montar ``DecisionContext`` usado pela metacognição."""

        quantum_packet = None
        if metacognition_input:
            packet = metacognition_input.get("current_q_signature_packet")
            if packet is None:
                packet = metacognition_input.get("captured_quantum_signature_packet")
            if packet is not None:
                quantum_packet = cast(QuantumSignaturePacket, packet)

        return DecisionContext(
            quantum_signature_packet=quantum_packet
            or QuantumSignaturePacket(vector=[], metrics={}),
            market_regime=(
                metacognition_input.get("market_regime", "unknown")
                if metacognition_input
                else "unknown"
            ),
            trade_details={
                "symbol": symbol,
                "action": decision_data.get("signal", "HOLD"),
                "confidence": decision_data.get("confidence", 0.0),
                "stop_loss": decision_data.get("stop_loss"),
                "take_profit": decision_data.get("take_profit"),
                "reasons": decision_data.get("reasons", []),
            },
            quantum_metrics_at_decision=(
                metacognition_input.get("quantum_metrics", {})
                if metacognition_input
                else {}
            ),
            current_universe_statevector=(
                metacognition_input.get("current_universe_statevector")
                if metacognition_input
                else None
            ),
            latest_universe_metrics=(
                metacognition_input.get("latest_universe_metrics", {})
                if metacognition_input
                else {}
            ),
            timestamp=datetime.now(timezone.utc),
        )

    def _apply_metacognition_result(
        self, symbol: str, metacognition_result: Optional[MetacognitionResult]
    ) -> None:
        """Aplicar resultado da metacognição e registrar logs."""

        if not metacognition_result:
            logger.info(
                f"Ciclo metacognitivo para {symbol} concluído, mas nenhum resultado foi retornado."
            )
            return

        trade_signal = metacognition_result.trade_signal
        updated_context = metacognition_result.context
        if updated_context.trade_directive == "REDUCE_EXPOSURE" and not any(
            self.open_positions.values()
        ):
            logger.debug("No positions open; ignoring REDUCE_EXPOSURE directive")
            updated_context.trade_directive = None
            if trade_signal and trade_signal.signal_type == "REDUCE_EXPOSURE":
                trade_signal.signal_type = "NO_SIGNAL"

        self._last_metacognitive_ctx = updated_context
        logger.info(
            "Contexto metacognitivo para %s atualizado com sucesso. Sinal: %s",
            symbol,
            trade_signal.signal_type if trade_signal else "None",
        )

    async def _run_metacognition_cycle(
        self, symbol: str, metacognition_input: Optional[Dict[str, Any]]
    ):
        """Executa o ciclo de metacognição para ``symbol``."""
        if self.disable_metacognition:
            logger.debug(f"Metacognição desabilitada. Pulando ciclo para {symbol}.")
            return

        if not self.metacognition_executive:
            logger.warning(
                f"Motor de Metacognição (metacognition_executive) não inicializado. Pulando ciclo para {symbol}."
            )
            return

        if metacognition_input is None:
            logger.info(
                f"Nenhuma entrada fornecida para o ciclo metacognitivo de {symbol}. O ciclo pode ser limitado."
            )
            # Mesmo sem input completo, pode haver lógica de auto-análise no módulo de metacognição.
            # No entanto, a implementação atual de assess_and_modulate_consciousness parece esperar dados.
            # Considerar passar um dicionário vazio ou apenas o símbolo se for o caso de uso.
            # return # Descomentar se assess_and_modulate_consciousness *sempre* precisar de input rico.

        try:
            logger.info(f"Iniciando ciclo metacognitivo para {symbol}...")

            decision_data_raw = (
                metacognition_input.get("decision") if metacognition_input else None
            )
            decision_data = self._parse_decision_data(decision_data_raw)

            decision_context = self._build_decision_context(
                symbol, decision_data, metacognition_input
            )

            metacognition_result = self.metacognition_executive.run_with_qast_feedback(
                decision_context=decision_context,
                pnl_feedback=(
                    metacognition_input.get("pnl_from_last_trade")
                    if metacognition_input
                    else None
                ),
            )

            self._apply_metacognition_result(symbol, metacognition_result)

            # HUD snapshot will be updated once per cycle in ``trading_loops``.

        except (RuntimeError, ValueError) as e:
            logger.exception(
                "Erro durante o ciclo metacognitivo para %s: %s",
                symbol,
                e,
            )

    async def _perform_health_check(self):
        """
        Executa verificação de saúde do sistema de trading.
        """
        span_cm = (
            trace.get_tracer(__name__).start_as_current_span(
                "trader.perform_health_check"
            )
            if trace
            else nullcontext()
        )
        if hasattr(span_cm, "__enter__"):
            span_cm.__enter__()
        try:
            current_time = time.time()

            # Verificar se é hora de fazer o health check (a cada 60 segundos)
            if hasattr(self, "_last_health_check"):
                if current_time - self._last_health_check < 60:
                    return

            self._last_health_check = current_time

            ticker_errors = getattr(self, "_ticker_error_count", 0)
            conn_errors = getattr(self, "_connection_failure_count", 0)
            if ticker_errors > MAX_ERROR_HISTORY:
                ticker_errors = MAX_ERROR_HISTORY
                self._ticker_error_count = MAX_ERROR_HISTORY
            if conn_errors or ticker_errors:
                self._consecutive_errors = min(
                    self._consecutive_errors + ticker_errors + conn_errors,
                    MAX_ERROR_HISTORY,
                )
                logger.warning(
                    "Health Check: %d erros de ticker e %d falhas de conexão recentes",
                    ticker_errors,
                    conn_errors,
                )

            if getattr(self, "_previous_cycle_exception", False):
                self._consecutive_errors = min(
                    self._consecutive_errors + 1, MAX_ERROR_HISTORY
                )
                degraded = True
            else:
                degraded = ticker_errors or conn_errors

            if not (
                ticker_errors
                or conn_errors
                or getattr(self, "_previous_cycle_exception", False)
            ):
                self._consecutive_errors = 0

            # Verificar conexão com a exchange se estiver em modo live ou paper_trading
            if self.mode in ["live", "paper_trading"] and self.exchange:
                try:
                    # Testar se a exchange ainda está responsiva
                    markets = getattr(self.exchange, "markets", None)
                    if not markets:
                        logger.warning("Health Check: Exchange markets não carregados")
                except (OSError, RuntimeError) as e:
                    logger.error(
                        "Health Check: Problema com conexão da exchange: %s",
                        e,
                    )

            # Verificar estado da carteira
            if hasattr(self, "wallet_state") and self.wallet_state:
                current_capital = self.wallet_state.get("current_capital", 0)
                if current_capital <= 0:
                    logger.warning("Health Check: Capital atual é zero ou negativo")

            # Verificar posições abertas
            if hasattr(self, "open_positions"):
                open_count = sum(len(p) for p in self.open_positions.values())
                if open_count > 10:  # Limite arbitrário
                    logger.warning(
                        f"Health Check: Muitas posições abertas: {open_count}"
                    )

            # Verificar erros consecutivos
            if hasattr(self, "_consecutive_errors") and self._consecutive_errors > 5:
                logger.warning(
                    f"Health Check: {self._consecutive_errors} erros consecutivos detectados"
                )

            if hasattr(self, "_consecutive_ticker_failures"):
                if self._consecutive_ticker_failures >= self.ticker_failure_limit:
                    logger.warning(
                        f"Health Check: {self._consecutive_ticker_failures} falhas consecutivas ao obter ticker"
                    )
                    self.system_health.ticker_fetch = (
                        f"failed ({self._consecutive_ticker_failures})"
                    )
                else:
                    self.system_health.ticker_fetch = "ok"

            self.system_health.status = (
                "degraded" if degraded or self._consecutive_errors else "ok"
            )

            # Log de status geral
            if degraded or self._consecutive_errors:
                logger.info(
                    "Health Check: Sistema degradado com %d falhas consecutivas",
                    self._consecutive_errors,
                )
            else:
                logger.debug("Health Check: Sistema operando normalmente")

            self._ticker_error_count = 0
            self._connection_failure_count = 0
            self._previous_cycle_exception = False

        except (RuntimeError, OSError) as e:
            logger.error(
                "Erro durante verificação de saúde: %s",
                e,
                exc_info=True,
            )
        finally:
            if hasattr(span_cm, "__exit__"):
                span_cm.__exit__(None, None, None)

    async def _process_trading_decision(
        self,
        symbol: str,
        decision: ScalpingDecision,  # Ou TempDecision, dependendo da implementação
        current_price: float,  # Este agora é o execution_trigger_price
        current_q_signature_packet_arg: Optional[
            QuantumSignaturePacket
        ],  # YAA: Alterado para aceitar o pacote
        market_snapshot_at_decision: Optional[Dict[str, Any]],
        decision_context_details: Optional[Dict[str, Any]],
        trace_id: Optional[str] = None,
    ):
        """Processa a decisão de trading para um símbolo."""
        tracer = get_tracer(__name__)
        with tracer.start_as_current_span(
            "trader.trade_decision.start",
            attributes=(
                {"symbol": symbol, "trace_id": trace_id}
                if trace_id
                else {"symbol": symbol}
            ),
        ):
            pass

        with tracer.start_as_current_span(
            "trader.trade_decision",
            attributes=(
                {"symbol": symbol, "trace_id": trace_id}
                if trace_id
                else {"symbol": symbol}
            ),
        ):
            await execution_engine.process_trading_decision(
                self,
                symbol,
                decision,
                current_price,
                current_q_signature_packet_arg,
                market_snapshot_at_decision,
                decision_context_details,
            )

        with tracer.start_as_current_span(
            "trader.trade_decision.end",
            attributes=(
                {"symbol": symbol, "trace_id": trace_id}
                if trace_id
                else {"symbol": symbol}
            ),
        ):
            pass

    async def _open_position(
        self,
        symbol: str,
        side: str,
        entry_price: float,  # Este é o preço de decisão/contexto
        size: float,
        stop_loss: Optional[float],
        take_profit: Optional[float],
        q_signature_packet_arg: Optional[QuantumSignaturePacket],
        market_snapshot: Optional[Dict[str, Any]],
        decision_context: Optional[Dict[str, Any]],
        limit_price: Optional[float] = None,
    ):
        """Abre uma nova posição (simulada ou real)."""

        return await execution_engine.open_position(
            self,
            symbol,
            side,
            entry_price,
            size,
            stop_loss,
            take_profit,
            q_signature_packet_arg,
            market_snapshot,
            decision_context,
            limit_price=limit_price,
        )

    async def _add_to_position(
        self,
        position: OpenPosition,
        additional_size: float,
        entry_price: float,
    ) -> OpenPosition:
        """Aumenta uma posição existente consolidando micro-lotes."""

        symbol = position.symbol
        actual_price = entry_price
        new_order_id = f"add_{int(time.time() * 1000)}"

        if self.mode == "live" and self.exchange:
            try:
                order_result = await self.exchange.create_order(
                    symbol, "market", position.side, additional_size
                )
                if order_result and order_result.get("id"):
                    new_order_id = order_result["id"]
                    if order_result.get("price") is not None:
                        actual_price = float(order_result["price"])
                else:
                    logger.error(
                        f"Falha ao ajustar posição LIVE para {symbol}. Resultado inesperado: {order_result}"
                    )
                    return position
            except (RuntimeError, ValueError, OSError) as e:
                logger.error(
                    "Erro ao ajustar posição LIVE para %s: %s",
                    symbol,
                    e,
                )
                return position
        elif self.mode == "paper_trading" and self.exchange:
            current_ticker = self.current_tickers.get(symbol, {})
            if position.side == "buy" and current_ticker.get("ask"):
                actual_price = float(current_ticker["ask"])
            elif position.side == "sell" and current_ticker.get("bid"):
                actual_price = float(current_ticker["bid"])
            new_order_id = f"paper_{new_order_id}"
        else:
            new_order_id = f"sim_{new_order_id}"

        async with self._pos_lock:
            weighted = (
                position.entry_price * position.size + actual_price * additional_size
            )
            position.size += additional_size
            position.entry_price = weighted / position.size
            position.order_id = new_order_id

            if hasattr(self, "wallet_state") and self.wallet_state:
                self.wallet_state["available_cash"] -= actual_price * additional_size
                self.wallet_state["positions_value"] += actual_price * additional_size
                self.wallet_state["current_capital"] = (
                    self.wallet_state["available_cash"]
                    + self.wallet_state["positions_value"]
                )

            self._save_open_positions()

        logger.info(
            f"Posição em {symbol} ajustada em +{additional_size:.6f} unidades (nova entrada {actual_price:.2f})"
        )
        return position

    async def _fetch_position_details(
        self, symbol: str, order_id: str
    ) -> Optional[Dict[str, Any]]:
        """Safely retrieve details of an open position.

        Parameters
        ----------
        symbol : str
            Market symbol for the position.
        order_id : str
            Identifier of the position.

        Returns
        -------
        Optional[Dict[str, Any]]
            Dictionary with position details or ``None`` if not found.
        """

        async with self._pos_lock:
            positions = self.open_positions.get(symbol, [])
            original_pos = next((p for p in positions if p.order_id == order_id), None)
            if original_pos is None:
                logger.warning(
                    f"Tentativa de fechar posição {order_id} para {symbol} que não está aberta. Ignorando."
                )
                return None

            return {
                "order_id": original_pos.order_id,
                "side": original_pos.side,
                "size": original_pos.size,
                "entry_price": original_pos.entry_price,
                "timestamp": original_pos.timestamp,
                "captured_quantum_signature_packet": original_pos.captured_quantum_signature_packet,
                "market_snapshot_at_decision": original_pos.market_snapshot_at_decision,
                "decision_context_details": original_pos.decision_context_details,
            }

    async def _execute_close_order(
        self,
        symbol: str,
        position: Dict[str, Any],
        trigger_price: float,
        trigger_price_current: Optional[float] = None,
    ) -> Tuple[float, bool, str]:
        """Execute the close order on the exchange or in simulation."""

        return await execution_engine.execute_close_order(
            self,
            symbol,
            position,
            trigger_price,
            trigger_price_current,
        )

    async def _finalize_close(
        self,
        symbol: str,
        order_id: str,
        exit_price: float,
        reason: str,
        details: Dict[str, Any],
        close_order_id: str,
    ) -> Dict[str, Any]:
        """Finalize the closing process updating internal state and wallet."""

        return await execution_engine.finalize_close(
            self,
            symbol,
            order_id,
            exit_price,
            reason,
            details,
            close_order_id,
        )

    async def _close_position(
        self, symbol: str, order_id: str, trigger_price: float, reason: str
    ):
        """Orchestrates closing of a position."""

        return await execution_engine.close_position(
            self, symbol, order_id, trigger_price, reason
        )

    async def _enforce_position_closure_rules(
        self, symbol: str, current_price: float
    ) -> None:
        """Fecha posições baseando-se na metacognição ou no limite máximo."""

        await position_manager.enforce_position_closure_rules(
            self, symbol, current_price
        )

    async def _initialize_exchange_connection(self) -> None:
        """Inicializa a conexão com a exchange."""

        await exchange_manager.initialize_exchange_connection(self)

        # Garantir que o QAST Core tenha referência à exchange já conectada
        if self.exchange and hasattr(self, "qast_core"):
            if not hasattr(self.qast_core, "exchanges"):
                # Criar dict se não existir por algum motivo
                self.qast_core.exchanges = {}
            # Utilize data_source como nome; fallback para "primary"
            ex_name = self.data_source.lower() if self.data_source else "primary"
            # Adapta a interface se o objeto não tiver método 'initialize' esperado pelo QAST Core
            if not hasattr(self.exchange, "initialize") and hasattr(self.exchange, "initialize_connection"):
                # Cria alias
                setattr(self.exchange, "initialize", self.exchange.initialize_connection)
            self.qast_core.exchanges[ex_name] = self.exchange
            # Também atualiza lista de símbolos configurados se estiver vazia
            if not self.qast_core.config.get("symbols"):
                self.qast_core.config["symbols"] = list(self.symbols)
            if not self.qast_core.config.get("timeframes"):
                self.qast_core.config["timeframes"] = list(self.timeframes)
            logger.debug("Exchange '%s' registrada no QAST Core", ex_name)

    async def _fetch_dynamic_fee(self) -> None:
        """Atualiza ``trading_fee_pct`` consultando a exchange."""

        self.trading_fee_pct = await fee_management.fetch_dynamic_fee(self)

    async def _preflight_exchange_check(self, *, keep_connection: bool = False) -> bool:
        """Realiza verificações básicas de acesso à exchange."""

        result = await exchange_setup.preflight_exchange_check(
            self, keep_connection=keep_connection
        )
        if not result:
            logger.error("Pré-flight na exchange falhou")
        return result

    async def _is_exchange_connected(self) -> bool:
        return await exchange_manager.is_exchange_connected(self)

    async def health_check(self) -> None:
        """Verifica a conexão com a exchange e reconecta se necessário."""

        await exchange_manager.health_check(self)

    async def close_exchange(self) -> None:
        """Close the current exchange connection and cleanup background tasks."""
        logger.info(
            "Ativando flag _prevent_reconnect; aguardando cancelamento de tarefas"
        )
        self._prevent_reconnect = True

        tasks = ("_position_monitor_task", "_qast_schedule_task")
        for attr in tasks:
            task = getattr(self, attr, None)
            if task is not None:
                task.cancel()

        for attr in tasks:
            task = getattr(self, attr, None)
            if task is not None:
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                setattr(self, attr, None)

        await exchange_manager.close_exchange(self)

        logger.info("Tarefas canceladas. Liberando flag _prevent_reconnect")
        self._prevent_reconnect = False

    def _parse_reduction_pct(self, signal: Any, default: float = 50.0) -> float:
        """Wrapper para :func:`utils.parse_reduction_pct`."""

        from .utils import parse_reduction_pct

        return parse_reduction_pct(signal, default)

    def _determine_poll_interval(self) -> float:
        """Calculate base poll interval derived from the smallest timeframe."""

        minutes = min(timeframe_to_minutes(tf) or 1 for tf in self.timeframes)
        if minutes == 15:
            return 60.0
        if minutes == 30:
            return 120.0
        return float(minutes * 60)

    def _adjust_poll_interval(self, success: bool) -> None:
        """Update ``current_poll_interval`` using exponential back-off."""

        if not getattr(self, "adaptive_poll", True):
            return

        if success:
            self._poll_backoff_level = 0
            self.current_poll_interval = self.default_poll_interval
            return

        self._poll_backoff_level += 1
        backoff = self.default_poll_interval * (2**self._poll_backoff_level)
        max_interval = self.default_poll_interval * 16
        self.current_poll_interval = min(backoff, max_interval)

    def _calculate_next_poll_time(self, now: Optional[datetime] = None) -> datetime:
        """Return the next time when a candle is expected for any timeframe.

        Parameters
        ----------
        now:
            Reference ``datetime``. If ``None``, the current UTC time is used.

        Returns
        -------
        datetime
            Moment when the next candle should be polled. The timezone of
            ``now`` is preserved.
        """

        now = now or datetime.now(timezone.utc)

        next_times = [
            now + timedelta(minutes=timeframe_to_minutes(tf) or 1)
            for tf in self.timeframes
        ]

        return min(next_times)

    async def _monitor_positions_once(self) -> None:
        """Check open positions for stop loss or take profit triggers."""
        await trading_loops.monitor_positions_once(self)

    def _ensure_risk_managers_initialized(self) -> None:
        """Verifica a existência de ``risk_managers`` para todos os símbolos.

        Levanta ``RuntimeError`` se algum símbolo não possuir um ``QUALIARiskManager``
        válido. Este método é executado durante a análise de mercado para garantir
        que a configuração de risco permaneça consistente ao longo da execução.
        """

        logger.debug("Verificando inicialização de risk managers...")

        missing_symbols = [
            s
            for s in self.symbols
            if s not in self.risk_managers or self.risk_managers[s] is None
        ]

        if missing_symbols:
            logger.error("Símbolos sem risk manager: %s", missing_symbols)
            raise RuntimeError(
                f"Risk managers ausentes para os símbolos: {missing_symbols}"
            )

        logger.debug(
            "Todos os símbolos possuem risk managers: %s",
            list(self.risk_managers.keys()),
        )

    async def _run_qast_evolution(self) -> None:
        """Delegates QAST evolution to :class:`TradingQASTCore`."""
        from .config import config

        await self.qast_core.run_qast_evolution(
            self.qast_engines,
            self.market_data,
            self.primary_timeframe,
            Path(config.results_dir),
        )

    async def _qast_scheduler_loop(self) -> None:
        """Background task running QAST evolution every configured interval."""
        interval = self.qast_schedule_interval_minutes * 60
        try:
            while not self.shutdown_event.is_set():
                try:
                    await asyncio.wait_for(self.shutdown_event.wait(), timeout=interval)
                except asyncio.TimeoutError:
                    pass

                if self.shutdown_event.is_set():
                    break

                await self._run_qast_evolution()
        except asyncio.CancelledError:
            logger.info("QAST scheduler loop cancelled")

    async def generate_performance_report(
        self, save_to_file: bool = True
    ) -> Dict[str, Any]:
        """
        Gera um relatório completo de desempenho do sistema.

        Args:
            save_to_file: Se True, salva o relatório em um arquivo JSON

        Returns:
            Dicionário com todas as métricas de performance
        """
        logger.info("Gerando relatório de performance...")

        # Obter métricas de performance
        metrics = self._calculate_performance_metrics()

        # Criar o relatório completo
        report = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "session_info": {
                "trader_id": self.trader_id,
                "mode": self.mode,
                "start_time": self.start_time.isoformat(),
                "duration": (
                    datetime.now(timezone.utc) - self.start_time
                ).total_seconds(),
                "symbols": self.symbols,
                "timeframes": self.timeframes,
                "risk_profile": self.risk_profile,
            },
            "performance_metrics": metrics,
            "trade_summary": {
                "total_trades": len(self.trade_history),
                "trades_by_symbol": {
                    symbol: len(
                        [t for t in self.trade_history if t["symbol"] == symbol]
                    )
                    for symbol in self.symbols
                },
                "trades_by_side": {
                    "buy": len(
                        [t for t in self.trade_history if t.get("side") == "buy"]
                    ),
                    "sell": len(
                        [t for t in self.trade_history if t.get("side") == "sell"]
                    ),
                },
            },
            "signal_metrics": self.signal_counters,
            "trade_history": self.trade_history,
            "final_wallet_state": self.wallet_state,
        }

        # Salvar relatório em arquivo se solicitado
        if save_to_file:
            timestamp_str = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            mode_str = self.mode
            report_type = "paper_trading" if mode_str == "paper_trading" else mode_str
            from .config import config

            filename = os.path.join(
                config.results_dir,
                f"qualia_{report_type}_report_{timestamp_str}.json",
            )

            # Garantir que o diretório existe
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            try:
                with open(filename, "w") as f:
                    json.dump(report, f, indent=2, default=str)
                logger.info("Relatório de performance salvo em: %s", filename)

                # Gerar gráficos apenas em paper_trading
                if self.mode == "paper_trading":
                    await self._generate_performance_charts(
                        report, filename.replace(".json", "")
                    )
            except OSError as exc:
                logger.error(
                    "Erro ao salvar relatório de performance em %s: %s",
                    filename,
                    exc,
                    exc_info=True,
                )

        return report

    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """Compute aggregated performance metrics from ``trade_history``."""

        initial_capital = self.wallet_state["initial_capital"]
        metrics = aggregate_trade_performance(
            self.trade_history,
            initial_capital,
            self.wallet_state.get("total_fees_paid", 0.0),
        )
        metrics["total_fees_paid"] = metrics.pop("total_fees")
        return metrics

    async def _generate_performance_charts(
        self, report: Dict[str, Any], base_filename: str
    ) -> None:
        """
        Gera gráficos ilustrativos de performance e os salva como arquivos PNG.

        Args:
            report: Relatório de performance completo
            base_filename: Nome base para os arquivos de gráficos
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates

            metrics = report["performance_metrics"]
            portfolio_values = metrics.get("portfolio_values", [])

            if not portfolio_values:
                logger.warning("Sem dados suficientes para gerar gráficos")
                return

            # Garantir que o diretório existe
            os.makedirs(os.path.dirname(base_filename), exist_ok=True)

            # 1. Gráfico de evolução do capital
            plt.figure(figsize=(12, 6))
            plt.plot(portfolio_values)
            plt.title("Evolução do Capital")
            plt.xlabel("Número de Trades")
            plt.ylabel("Capital ($)")
            plt.grid(True)
            plt.savefig(f"{base_filename}_capital.png")
            plt.close()

            # 2. Gráfico de PnL por trade
            pnl_values = [trade.get("pnl", 0.0) for trade in report["trade_history"]]

            if pnl_values:
                plt.figure(figsize=(12, 6))
                plt.bar(
                    range(len(pnl_values)),
                    pnl_values,
                    color=["green" if p > 0 else "red" for p in pnl_values],
                )
                plt.title("PnL por Trade")
                plt.xlabel("Número do Trade")
                plt.ylabel("PnL ($)")
                plt.grid(True)
                plt.savefig(f"{base_filename}_pnl_per_trade.png")
                plt.close()

            # 3. Gráfico de estatísticas de trades
            if report["trade_summary"]["total_trades"] > 0:
                labels = ["Ganhos", "Perdas"]
                sizes = [metrics["winning_trades"], metrics["losing_trades"]]

                plt.figure(figsize=(10, 6))
                plt.pie(
                    sizes,
                    labels=labels,
                    autopct="%1.1f%%",
                    colors=["green", "red"],
                )
                plt.title("Distribuição de Trades")
                plt.savefig(f"{base_filename}_trade_distribution.png")
                plt.close()

            logger.info(f"Gráficos de performance gerados em: {base_filename}_*.png")

        except ImportError:
            logger.warning(
                "Matplotlib não está instalado. Não foi possível gerar gráficos."
            )
        except OSError as exc:
            logger.error(
                "Erro ao salvar gráficos de performance em %s: %s",
                base_filename,
                exc,
                exc_info=True,
            )
        except (ValueError, RuntimeError) as exc:
            logger.error(
                "Erro ao gerar gráficos de performance: %s",
                exc,
                exc_info=True,
            )

    async def run(self) -> None:
        """
        Ponto de entrada principal para iniciar o trader.
        Orquestra a inicialização da conexão, carregamento de estado e o loop principal.
        """
        self._critical_failure_handled = False
        try:
            await self._initialize_exchange_connection()
            self._load_open_positions()
            await self.qast_core.initialize()

            end_time = (
                datetime.now(timezone.utc) + timedelta(seconds=self.duration_seconds)
                if self.duration_seconds
                else None
            )
            while True:
                await self.qast_core.run_cycle()
                if end_time and datetime.now(timezone.utc) >= end_time:
                    break
                await asyncio.sleep(self.poll_interval_override or 1.0)
        finally:
            if self.exchange is not None:
                await self.close_exchange()
            if self.qpm_memory:
                self.qpm_memory.close()

    async def start(self, *, skip_preflight: bool = False) -> None:
        """Inicializa e executa o trader com verificação opcional."""

        if not skip_preflight:
            ok = await self._preflight_exchange_check(keep_connection=True)
            if not ok:
                self.logger.error(
                    "Pré-flight falhou. Verifique conexão ou credenciais e tente novamente."
                )
                return

        await self.initialize()
        await self.run()

    def _load_risk_profile_settings(self) -> None:
        """Carrega configurações do perfil de risco com validação robusta e fallback padrão."""
        from .config.risk_validator import RiskParameterValidator

        validator = RiskParameterValidator()
        profiles: Dict[str, Any] = {}

        # 1) Tentar usar o que já foi carregado do arquivo de estratégia
        if self.loaded_strategy_configs:
            profiles = self.loaded_strategy_configs.get("risk_profile_settings", {})

        # 2) Se ainda estiver vazio e o caminho existir, carregar diretamente
        if not profiles and self.strategy_config_path:
            try:
                with open(self.strategy_config_path, "r", encoding="utf-8") as fh:
                    file_cfg = json.load(fh)
                profiles = file_cfg.get("risk_profile_settings", {})
            except OSError as exc:
                logger.error(
                    "Erro ao carregar risk_profile_settings de %s: %s",
                    self.strategy_config_path,
                    exc,
                )
            except json.JSONDecodeError as exc:
                logger.warning(
                    "JSON inválido em %s: %s. Usando configurações padrão.",
                    self.strategy_config_path,
                    exc,
                )
                profiles = validator._get_fallback_profiles()

        # 3) Merge/override com valores fornecidos via self.config
        config_profiles = self.config.get("risk_profile_settings", {})
        for key, value in config_profiles.items():
            if key in profiles and isinstance(profiles[key], dict):
                profiles[key].update(value)
            else:
                profiles[key] = value

        # 4) TASK 4: Validação robusta com correção automática
        validation_result = validator.validate_risk_profiles(profiles)

        if not validation_result.is_valid or validation_result.applied_fallbacks:
            logger.info("🔧 TASK 4: Aplicando correções nos parâmetros de risco")

            # Log das correções aplicadas
            if validation_result.applied_fallbacks:
                logger.info("Correções aplicadas:")
                for fallback in validation_result.applied_fallbacks:
                    logger.info(f"  - {fallback}")

            if validation_result.warnings:
                logger.warning("Avisos durante validação:")
                for warning in validation_result.warnings:
                    logger.warning(f"  - {warning}")

            if validation_result.errors:
                logger.error("Erros encontrados:")
                for error in validation_result.errors:
                    logger.error(f"  - {error}")

            # Usar configuração corrigida
            profiles = validation_result.corrected_config.get(
                "risk_profile_settings", {}
            )

            # Salvar correções no arquivo se possível
            if self.strategy_config_path and validation_result.applied_fallbacks:
                try:
                    # Carregar arquivo completo
                    with open(self.strategy_config_path, "r", encoding="utf-8") as fh:
                        full_config = json.load(fh)

                    # Atualizar seção de risco
                    full_config["risk_profile_settings"] = profiles

                    # Salvar com backup
                    backup_path = self.strategy_config_path + ".backup"
                    with open(backup_path, "w", encoding="utf-8") as backup_fh:
                        json.dump(full_config, backup_fh, indent=4, ensure_ascii=False)

                    with open(self.strategy_config_path, "w", encoding="utf-8") as fh:
                        json.dump(full_config, fh, indent=4, ensure_ascii=False)

                    logger.info(
                        f"Configuração corrigida salva em: {self.strategy_config_path}"
                    )
                    logger.info(f"Backup criado em: {backup_path}")

                except (OSError, PermissionError, json.JSONDecodeError) as exc:
                    logger.warning(f"Não foi possível salvar correções: {exc}")
        else:
            logger.info("✅ TASK 4: Parâmetros de risco validados com sucesso")

        if not profiles:
            logger.warning(
                "Nenhuma configuração de perfil de risco encontrada. Utilizando fallback padrão."
            )
            profiles = validator._get_fallback_profiles()

        self.risk_profile_configs = profiles

    async def _fetch_ohlcv(
        self, symbol: str, timeframe: str, trace_id: Optional[str] = None
    ) -> Optional[pd.DataFrame]:
        """Busca dados OHLCV incrementalmente, preservando o histórico existente.

        Este método é crucial para manter a continuidade dos dados entre ciclos.
        Em vez de buscar todos os dados novamente, ele:
        1. Verifica se já existem dados no market_data
        2. Se existirem, busca apenas os candles após o último timestamp
        3. Se não existirem, busca um histórico inicial adequado
        """
        if not self.exchange:
            logger.error("Exchange não inicializada para buscar dados OHLCV")
            return None

        try:
            # Verificar dados existentes
            existing_df = self.market_data.get(symbol, {}).get(timeframe)

            # Determinar o since baseado nos dados existentes
            since_ts = None
            if existing_df is not None and not existing_df.empty:
                # Pegar o timestamp do último candle
                last_candle = existing_df.iloc[-1]
                if isinstance(last_candle["timestamp"], pd.Timestamp):
                    last_ts = int(last_candle["timestamp"].timestamp() * 1000)
                else:
                    last_ts = int(last_candle["timestamp"])

                # YAA: Verificar se o timestamp não é futuro antes de calcular since_ts
                current_time_ms = int(time.time() * 1000)
                if last_ts > current_time_ms:
                    logger.warning(
                        "Timestamp do último candle é futuro para %s@%s: %s (atual: %s). Usando busca sem since.",
                        symbol,
                        timeframe,
                        last_ts,
                        current_time_ms,
                    )
                    since_ts = None  # Não usar since se timestamp for futuro
                else:
                    # Buscar a partir do próximo candle
                    timeframe_ms = timeframe_to_milliseconds(timeframe)
                    calculated_since = last_ts + timeframe_ms

                    # YAA: Verificar se o since calculado não é futuro
                    if calculated_since > current_time_ms:
                        logger.warning(
                            "Since calculado é futuro para %s@%s: %s (atual: %s). Usando busca sem since.",
                            symbol,
                            timeframe,
                            calculated_since,
                            current_time_ms,
                        )
                        since_ts = None  # Não usar since se for futuro
                    else:
                        since_ts = calculated_since

                if since_ts is not None:
                    logger.debug(
                        "Buscando dados incrementais para %s@%s a partir de %s",
                        symbol,
                        timeframe,
                        pd.Timestamp(since_ts, unit="ms"),
                    )
                else:
                    logger.debug(
                        "Buscando dados mais recentes para %s@%s (sem since devido a timestamp futuro)",
                        symbol,
                        timeframe,
                    )

                # Para busca incremental, limitar a quantidade
                limit = 100  # Buscar apenas os últimos 100 candles
            else:
                # Primeira busca - pegar histórico suficiente
                strategy = self.strategies.get(symbol)
                required = getattr(strategy, "required_initial_data_length", 336)
                limit = max(required, 336)

                logger.debug(
                    "Primeira busca de dados para %s@%s, limit=%s",
                    symbol,
                    timeframe,
                    limit,
                )

            # Buscar os dados
            df = await self.exchange.fetch_ohlcv(
                symbol, timeframe, since=since_ts, limit=limit
            )

            if df is None or df.empty:
                logger.warning(
                    "Nenhum dado novo retornado para %s@%s", symbol, timeframe
                )
                return existing_df  # Retornar dados existentes se não houver novos

            logger.debug(
                "Dados OHLCV obtidos para %s@%s: %s candles novos",
                symbol,
                timeframe,
                len(df),
            )

            combined, _ = self._merge_ohlcv_data(
                symbol, timeframe, existing_df, df, limit
            )
            self.market_data.setdefault(symbol, {})[timeframe] = combined

            return combined

        except (
            ccxt.NetworkError,
            ccxt.ExchangeError,
            asyncio.TimeoutError,
            ValueError,
        ) as exc:
            logger.error(
                "Erro ao buscar dados OHLCV para %s@%s: %s",
                symbol,
                timeframe,
                exc,
                exc_info=True,
            )
            return None

    async def _ensure_complete_historical_data(self) -> None:
        """Garante que todos os dados históricos necessários estejam carregados ANTES de criar estratégias.

        YAA CORREÇÃO CRÍTICA: Esta função resolve o problema de histórico insuficiente
        calculando os requisitos exatos de cada estratégia e carregando todos os dados
        necessários ANTES de iniciar o loop de trading.
        """
        logger.info(
            "🔄 Iniciando verificação e carregamento completo de dados históricos..."
        )

        # 1. Calcular requisitos preliminares para cada símbolo
        symbol_requirements = {}

        for symbol in self.symbols:
            # Obter configuração de estratégia para este símbolo
            strat_cfg = self.loaded_strategy_configs.get("strategy_config", {})
            strategy_alias = strat_cfg.get("name", "NovaEstrategiaQUALIA")
            strategy_params = strat_cfg.get("params", {})

            # YAA CORREÇÃO CRÍTICA: Usar valor alto fixo para garantir dados suficientes
            # Evita problemas de criação de estratégia temporária
            required_candles = 1000  # Valor seguro que deve cobrir todas as estratégias

            symbol_requirements[symbol] = {
                "required_candles": required_candles,
                "strategy_name": strategy_alias,
                "current_available": 0,
            }

            logger.info(
                f"📊 {symbol}: {strategy_alias} requer {required_candles} candles (padrão seguro)"
            )

        # 2. Verificar dados atualmente disponíveis
        total_required = 0
        total_available = 0

        for symbol in symbol_requirements:
            req = symbol_requirements[symbol]
            df = self.market_data.get(symbol, {}).get(self.primary_timeframe)
            available = len(df) if isinstance(df, pd.DataFrame) and not df.empty else 0

            req["current_available"] = available
            total_required += req["required_candles"]
            total_available += available

            logger.info(
                f"📈 {symbol}: {available}/{req['required_candles']} candles disponíveis"
            )

        # 3. Carregamento inteligente dos dados faltantes
        symbols_needing_data = [
            symbol
            for symbol, req in symbol_requirements.items()
            if req["current_available"] < req["required_candles"]
        ]

        if symbols_needing_data:
            logger.warning(
                f"⚠️  {len(symbols_needing_data)} símbolos precisam de dados adicionais"
            )

            # Usar AdaptiveHistoryManager para carregamento inteligente
            for symbol in symbols_needing_data:
                req = symbol_requirements[symbol]
                missing = req["required_candles"] - req["current_available"]

                logger.info(
                    f"🔍 Carregando {missing} candles adicionais para {symbol}..."
                )

                try:
                    # YAA CORREÇÃO CRÍTICA: Busca direta e simples da exchange
                    logger.info(
                        f"🔄 Buscando {req['required_candles']} candles para {symbol}..."
                    )

                    df = await self.exchange.fetch_ohlcv(
                        symbol,
                        self.primary_timeframe,
                        limit=req["required_candles"],
                    )

                    if df is not None and len(df) > 0:
                        if not isinstance(df, pd.DataFrame):
                            df = pd.DataFrame(
                                df,
                                columns=[
                                    "timestamp",
                                    "open",
                                    "high",
                                    "low",
                                    "close",
                                    "volume",
                                ],
                            )
                            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")

                        # Atualizar market_data diretamente
                        self.market_data.setdefault(symbol, {})[
                            self.primary_timeframe
                        ] = df
                        req["current_available"] = len(df)

                        logger.info(
                            f"✅ {symbol}: {len(df)} candles carregados com sucesso"
                        )

                        # Se não conseguiu todos os dados necessários, aceitar o que conseguiu
                        if len(df) < req["required_candles"]:
                            logger.warning(
                                f"⚠️ {symbol}: Exchange limitou a {len(df)}/{req['required_candles']} candles"
                            )
                            req["required_candles"] = len(df)  # Ajustar expectativa
                    else:
                        logger.error(f"❌ {symbol}: Exchange retornou dados vazios")
                        raise DataRequirementsError(
                            f"Exchange não retornou dados para {symbol}"
                        )

                except (
                    ccxt.NetworkError,
                    ccxt.ExchangeError,
                    ValueError,
                ) as e:
                    logger.error(f"❌ Erro ao carregar dados para {symbol}: {e}")
                    # Tentar usar dados já existentes se houver pelo menos alguns
                    if req["current_available"] >= 100:  # Mínimo absoluto reduzido
                        logger.warning(
                            f"🔧 Usando {req['current_available']} candles existentes para {symbol}"
                        )
                        req["required_candles"] = req["current_available"]
                    else:
                        raise DataRequirementsError(
                            f"Falha crítica no carregamento de dados para {symbol}: {e}"
                        )

        # 4. Validação final e logging de sucesso
        total_final = 0
        for symbol, req in symbol_requirements.items():
            df = self.market_data.get(symbol, {}).get(self.primary_timeframe)
            final_available = (
                len(df) if isinstance(df, pd.DataFrame) and not df.empty else 0
            )
            total_final += final_available

            logger.info(f"✅ {symbol}: {final_available} candles carregados")

        logger.info("🎉 Carregamento completo finalizado!")
        logger.info(f"📊 Total de candles carregados: {total_final}")
        logger.info(f"📈 Símbolos processados: {len(self.symbols)}")

        # Garantir que pelo menos alguns dados estão disponíveis
        if total_final == 0:
            raise DataRequirementsError(
                "Nenhum dado histórico foi carregado para qualquer símbolo"
            )

    async def _validate_exchange_limits(self) -> None:
        """Validate que os requisitos de histórico são compatíveis com limites da exchange."""

        for symbol, strategy in self.strategies.items():
            required = getattr(strategy, "required_initial_data_length", 0)
            if required <= 0:
                continue

            if self.exchange and hasattr(self.exchange, "max_history_candles"):
                try:
                    available = await self.exchange.max_history_candles(
                        symbol, self.primary_timeframe
                    )

                    if available < required:
                        logger.warning(
                            "Requisito de histórico (%s) excede disponibilidade da exchange (%s) para %s@%s",
                            required,
                            available,
                            symbol,
                            self.primary_timeframe,
                        )

                        # Tentativa de ajuste automático se a estratégia suporta
                        if hasattr(strategy, "s3_tsvf_window") and available > 0:
                            original_window = strategy.s3_tsvf_window
                            adjusted_window = max(
                                1, int(original_window * available / required)
                            )
                            strategy.s3_tsvf_window = adjusted_window

                            logger.info(
                                "Auto-ajustando s3_tsvf_window de %s para %s baseado em limit=%s",
                                original_window,
                                adjusted_window,
                                available,
                            )

                            # Recalcular requisito após ajuste
                            required = getattr(
                                strategy, "required_initial_data_length", required
                            )

                        if available < required:
                            raise DataRequirementsError(
                                f"Exchange {self.data_source} fornece apenas {available} candles "
                                f"para {symbol}@{self.primary_timeframe}, mas estratégia requer {required}"
                            )

                    logger.info(
                        "Validação OK: %s candles disponíveis vs %s requeridos para %s@%s",
                        available,
                        required,
                        symbol,
                        self.primary_timeframe,
                    )

                except InsufficientHistoryError:
                    logger.critical(
                        "Exchange não possui histórico disponível para %s@%s",
                        symbol,
                        self.primary_timeframe,
                    )
                    raise


async def main(args):
    """
    Função principal para configurar e executar o QUALIARealTimeTrader.
    """
    # O logger já está configurado no bloco __main__ antes desta função ser
    # chamada.
    logger.info(f"Iniciando QUALIARealTimeTrader com os seguintes argumentos: {args}")

    # As variáveis de ambiente são carregadas no bloco ``__main__`` por
    # ``load_environment()``. Nenhuma chamada adicional é necessária aqui.

    # Selecionar credenciais com prioridade: CLI depois ambiente
    if args.data_source == "kraken":
        final_api_key = args.kraken_api_key or get_env("KRAKEN_API_KEY")
        final_secret_key = args.kraken_secret_key or get_env("KRAKEN_SECRET_KEY")
        final_passphrase = None
        logger.info(
            f"[CREDENTIAL_CHECK] Chave de API Kraken sendo usada: {'SIM' if final_api_key else 'NÃO'}"
        )
        logger.info(
            f"[CREDENTIAL_CHECK] Chave Secreta Kraken sendo usada: {'SIM' if final_secret_key else 'NÃO'}"
        )
    elif args.data_source == "kucoin":
        final_api_key = args.kucoin_api_key or get_env("KUCOIN_API_KEY")
        final_secret_key = args.kucoin_secret_key or get_env("KUCOIN_SECRET_KEY")
        final_passphrase = args.kucoin_passphrase or get_env("KUCOIN_PASSPHRASE")
        logger.info(
            f"[CREDENTIAL_CHECK] Chave de API Kucoin sendo usada: {'SIM' if final_api_key else 'NÃO'}"
        )
        logger.info(
            f"[CREDENTIAL_CHECK] Chave Secreta Kucoin sendo usada: {'SIM' if final_secret_key else 'NÃO'}"
        )
    else:
        final_api_key = final_secret_key = final_passphrase = None

    validate_secret_key()

    try:
        from .config import config

        # CORREÇÃO YAA: Importar a função setup_core_components
        from .init_utils import setup_core_components

        core_components = await setup_core_components(
            config=config,
            symbols=args.symbols,
            timeframes=args.timeframes,
        )
        trader = QUALIARealTimeTrader(
            symbols=args.symbols,
            timeframes=args.timeframes,
            capital=args.capital,
            risk_profile=args.risk_profile,
            mode=args.mode,
            data_source=args.data_source,
            duration_seconds=args.duration_seconds,
            max_qpm_memory_size=args.max_qpm_memory_size,
            base_directory=args.base_directory,
            strategy_config_path=args.strategy_config_path,
            disable_metacognition=args.disable_metacognition,
            # YAA: Passar as chaves finais que consideram CLI e .env
            kraken_api_key=(final_api_key if args.data_source == "kraken" else None),
            kraken_secret_key=(
                final_secret_key if args.data_source == "kraken" else None
            ),
            kucoin_api_key=(final_api_key if args.data_source == "kucoin" else None),
            kucoin_secret_key=(
                final_secret_key if args.data_source == "kucoin" else None
            ),
            kucoin_passphrase=(
                final_passphrase if args.data_source == "kucoin" else None
            ),
            risk_per_trade_pct=args.risk_per_trade_pct,
            qast_historical_data_path=args.qast_historical_data_path,
            trader_id=args.trader_id,
            trading_fee_pct=args.fee_pct,
            ticker_fetch_timeout=args.ticker_fetch_timeout,
            poll_interval_override=args.poll_interval_override,
            adaptive_poll=args.adaptive_poll,
            api_fail_threshold=args.api_fail_threshold,
            api_recovery_timeout=args.api_recovery_timeout,
            enable_hud=args.enable_hud,
            use_webgpu=args.use_webgpu,
            qualia_universe=core_components["universe"],
            qpm_memory=core_components["qpm"],
            analysis_core=core_components["consciousness"],
        )

        try:
            # Inicializar componentes assíncronos do sistema
            await trader.start(skip_preflight=args.skip_preflight)

            # --- YAA: Reconfiguração do Nível de Log ---
            log_level_cli = args.log_level
            log_level_json = trader.config.get("log_level_from_config")

            chosen_name, numeric_level = resolve_log_level(
                log_level_cli, log_level_json
            )

            # Ajuste no logger raiz para evitar erro "missing 1 required positional
            # argument: 'name'" quando ``get_logger`` é chamado sem parâmetros.
            root_logger = get_logger(__name__)
            apply_log_level(
                numeric_level,
                [logging.getLogger(), root_logger],
            )

            logger.info(
                "Nível de logging final configurado para: %s (Valor numérico: %s)",
                chosen_name,
                numeric_level,
            )
            # --- Fim da Reconfiguração do Nível de Log ---

            # Assumindo que _main_loop é o método correto para iniciar o ciclo.
            # Se houver um método público como run_trading_cycle(), prefira-o.
            logger.info(f"Iniciando QUALIA Trader no modo: {trader.mode}...")
            await trader.run()
        finally:
            await trader.close_exchange()
    except asyncio.CancelledError:
        logger.info("Loop de trading cancelado.")
    except KeyboardInterrupt:
        logger.info("Execução interrompida pelo usuário (KeyboardInterrupt).")
    except (RuntimeError, OSError, ValueError) as e:
        logger.error(
            "Erro durante a execução do QUALIARealTimeTrader: %s",
            e,
            exc_info=True,
        )
        # Log explícito do traceback para debug
        logger.debug(traceback.format_exc())

    async def _init_live_feed_integration(self) -> None:
        """
        YAA: D-03.2 - Inicializa a integração do live feed com o sistema de trading.

        Conecta o sistema de live feed validado (D-03.1: 100% sucesso) com o
        QUALIATradingSystem existente, permitindo dados em tempo real do KuCoin.
        """
        try:
            logger.info("🚀 D-03.2: Inicializando Live Feed Integration...")

            # Verificar se live feed está habilitado na configuração
            live_feed_config = self.config.get("live_feed", {})
            if not live_feed_config.get("enabled", False):
                logger.info("Live feed desabilitado na configuração")
                return

            # Extrair configurações do live feed
            integration_config = {
                "mode": live_feed_config.get("mode", "hybrid"),
                "enable_live_feed": True,
                "live_feed_priority": live_feed_config.get("live_feed_priority", 1),
                "enable_fallback": live_feed_config.get("enable_fallback", True),
                "fallback_timeout": live_feed_config.get("fallback_timeout", 30.0),
                "enable_data_validation": live_feed_config.get("enable_data_validation", True),
                "price_variance_threshold": live_feed_config.get("price_variance_threshold", 0.01),
                "update_frequency": live_feed_config.get("update_frequency", 1.0),
                "batch_processing": live_feed_config.get("batch_processing", True),
                "paper_trading_mode": live_feed_config.get("paper_trading_mode", True),
                "paper_trading_symbols": live_feed_config.get("paper_trading_symbols", self.symbols),

                # Parâmetros otimizados
                "news_amp": live_feed_config.get("optimized_params", {}).get("news_amp", 11.3),
                "price_amp": live_feed_config.get("optimized_params", {}).get("price_amp", 1.0),
                "min_conf": live_feed_config.get("optimized_params", {}).get("min_conf", 0.37),
            }

            # Criar integração
            self.live_feed_integration = LiveFeedIntegration(
                trading_system=self,
                config=integration_config
            )

            # Inicializar com símbolos do sistema
            symbols_for_feed = [s.replace("/", "-") for s in self.symbols]  # Converter para formato exchange
            success = await self.live_feed_integration.initialize(
                symbols=symbols_for_feed,
                timeframes=self.timeframes
            )

            if success:
                # Iniciar integração
                start_success = await self.live_feed_integration.start()
                if start_success:
                    logger.info("✅ D-03.2: Live Feed Integration inicializada e ativa")

                    # Log do status da integração
                    status = self.live_feed_integration.get_integration_status()
                    logger.info(f"📊 Status: Live Feed Ativo: {status['live_feed_active']}, "
                              f"Fallback Ativo: {status['fallback_active']}, "
                              f"Modo: {status['config']['mode']}")
                else:
                    logger.warning("⚠️ Falha ao iniciar Live Feed Integration, continuando sem live feed")
            else:
                logger.warning("⚠️ Falha ao inicializar Live Feed Integration, continuando sem live feed")

        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Live Feed Integration: {e}")
            logger.info("Sistema continuará funcionando sem live feed")


if __name__ == "__main__":
    from pathlib import Path

    from .cli import parse_args

    args = parse_args()

    if args.cb_fail_threshold is not None:
        os.environ["QUALIA_CB_FAIL_THRESHOLD"] = str(args.cb_fail_threshold)
    if args.call_max_retries is not None:
        os.environ["QUALIA_CALL_MAX_RETRIES"] = str(args.call_max_retries)

    load_environment(args.env_path)

    project_root = PROJECT_ROOT
    from .config import config

    if args.strategy_config_path and not os.path.isabs(args.strategy_config_path):
        potential_path = project_root / args.strategy_config_path
        if potential_path.exists():
            args.strategy_config_path = str(potential_path)
        else:
            logger.warning(
                "strategy_config_path '%s' não pôde ser resolvido em relação a '%s'. Usando caminho informado.",
                args.strategy_config_path,
                project_root,
            )

    log_file_path = Path(config.trading_log_file)
    setup_logging({"level": args.log_level, "file": str(log_file_path)})
    configure_tracing()

    (project_root / "data" / "historical").mkdir(parents=True, exist_ok=True)
    Path(config.results_dir).mkdir(parents=True, exist_ok=True)
    Path(config.runs_json_dir).mkdir(parents=True, exist_ok=True)

    try:
        asyncio.run(main(args))
    except KeyboardInterrupt:
        logger.info("Execução interrompida pelo usuário (KeyboardInterrupt).")
    except (RuntimeError, OSError, ValueError) as e:
        logger.error(
            "Erro durante a execução do QUALIARealTimeTrader: %s",
            e,
            exc_info=True,
        )
        logger.debug(traceback.format_exc())
