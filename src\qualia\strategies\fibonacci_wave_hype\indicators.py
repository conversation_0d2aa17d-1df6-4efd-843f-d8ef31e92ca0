"""Indicadores Fibonacci para detecção de ondas de hype."""

import numpy as np
import pandas as pd
from typing import Dict, Any

def calculate_fibonacci_levels(highs: pd.Series, lows: pd.Series) -> Dict[str, float]:
    """Calcula níveis de Fibonacci para o período."""
    high = highs.max()
    low = lows.min()
    diff = high - low
    
    return {
        "0.0": high,
        "0.236": high - 0.236 * diff,
        "0.382": high - 0.382 * diff,
        "0.618": high - 0.618 * diff,  # Golden ratio
        "1.0": low,
        "support": low,
        "resistance": high,
    }

def detect_wave_patterns(df: pd.DataFrame, fib_levels: Dict) -> Dict[str, Any]:
    """Detecta padrões de ondas baseados em Fibonacci."""
    current_price = df["close"].iloc[-1]
    
    # Determina em qual nível de Fibonacci estamos
    fib_level = None
    for level, price in fib_levels.items():
        if level in ["0.236", "0.382", "0.618"]:
            if abs(current_price - price) < (fib_levels["0.0"] - fib_levels["1.0"]) * 0.02:
                fib_level = level
                break
    
    # Detecta direção da onda
    recent_trend = df["close"].tail(10).pct_change().mean()
    direction = 1 if recent_trend > 0 else -1
    
    return {
        "fib_level": fib_level or "none",
        "direction": direction,
        "trend_strength": abs(recent_trend),
    }

def calculate_hype_momentum(df: pd.DataFrame, wave_patterns: Dict) -> float:
    """Calcula momentum de hype baseado em volume e volatilidade."""
    if "volume" not in df.columns:
        return 0.5
    
    # Volume momentum
    vol_sma = df["volume"].tail(20).mean()
    current_vol = df["volume"].iloc[-1]
    vol_ratio = current_vol / vol_sma if vol_sma > 0 else 1.0
    
    # Price momentum
    price_momentum = wave_patterns["trend_strength"]
    
    # Combina fatores
    hype_momentum = (vol_ratio * 0.6 + price_momentum * 0.4)
    return min(hype_momentum, 2.0) / 2.0  # Normaliza para [0,1]