#!/usr/bin/env python3
"""
Framework Unificado de Backtesting QUALIA
YAA IMPLEMENTATION: Sistema completo de análise comparativa de estratégias
"""

import pandas as pd
import numpy as np
import json
import warnings
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import sys
import traceback
import time

warnings.filterwarnings('ignore')

# Configura ambiente de backtesting
try:
    from setup_backtest_environment import setup_environment, create_mock_config, patch_qualia_imports
    setup_environment()
    patch_qualia_imports()
    BACKTEST_CONFIG = create_mock_config()
except ImportError:
    # Fallback manual
    sys.path.append(str(Path(__file__).parent.parent))
    sys.path.append(str(Path(__file__).parent.parent / "src"))
    BACKTEST_CONFIG = {}

try:
    from qualia.strategies.strategy_factory import StrategyFactory
    from qualia.strategies.strategy_interface import TradingContext
    from qualia.strategies.params import QualiaTSVFParams
    from qualia.config.config_manager import ConfigManager
    from qualia.utils.logger import get_logger
    from qualia.risk.manager import create_risk_manager, QUALIARiskManager
    from qualia.memory.experience_replay import ExperienceReplay
    from qualia.memory.system import MemorySystem
    from qualia.metrics.performance_metrics import (
        calculate_sharpe_ratio,
        calculate_sortino_ratio,
        calculate_max_drawdown,
        calculate_cagr,
        calculate_quantum_advantage
    )
    QUALIA_IMPORTS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Importação parcial: {e}")
    print("🔄 Continuando com funcionalidades básicas...")
    QUALIA_IMPORTS_AVAILABLE = False

@dataclass
class BacktestConfig:
    """Configuração para backtesting."""
    initial_capital: float = 100000.0
    risk_per_trade_pct: float = 2.0
    commission_rate: float = 0.001  # 0.1%
    slippage_rate: float = 0.0005   # 0.05%
    min_lookback_periods: int = 100

@dataclass
class PerformanceMetrics:
    """Métricas de performance padronizadas."""
    strategy_name: str
    symbol: str
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    cagr: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_duration: float
    quantum_advantage: float = 0.0
    cross_modal_coherence: float = 0.0
    final_capital: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário."""
        return asdict(self)

class UnifiedBacktestingFramework:
    """Framework unificado para backtesting de estratégias QUALIA."""

    def __init__(self, config: BacktestConfig = None):
        self.config = config or BacktestConfig()
        self.logger = self._setup_logger()
        self.results = {}

        # Estratégias disponíveis com seus aliases
        self.available_strategies = {
            'QualiaTSVFStrategy': 'NovaEstrategiaQUALIA',
            'EnhancedQuantumMomentumStrategy': 'quantum_momentum_v2',
            'QuantumTrendReversalStrategy': 'q_reversal',
            'FibonacciWaveHypeStrategy': 'fibonacci_wave_hype',
            'CompositeStrategy': 'composite_v1',
            'QUALIAEnhancedScalpingStrategy': 'enhanced_scalping'
        }

        # Inicializa dependências do QUALIA
        self.risk_manager = None
        self.memory_system = None
        self._setup_qualia_dependencies()

    def _setup_logger(self):
        """Configura logger básico."""
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)

    def _setup_qualia_dependencies(self):
        """Configura dependências necessárias do sistema QUALIA."""
        if not QUALIA_IMPORTS_AVAILABLE:
            self.logger.warning("⚠️  Dependências QUALIA não disponíveis, usando mocks")
            self._setup_mock_dependencies()
            return

        try:
            # Configura RiskManager para backtesting
            self.logger.info("🛡️  Configurando RiskManager para backtesting...")
            self.risk_manager = create_risk_manager(
                initial_capital=self.config.initial_capital,
                risk_profile="balanced",
                custom_risk_per_trade_pct=self.config.risk_per_trade_pct
            )

            # Configura MemorySystem básico
            self.logger.info("🧠 Configurando MemorySystem...")
            memory_config = {
                "qpm_memory_file": None,  # Não persiste em backtesting
                "experimental_enabled": False
            }
            self.memory_system = MemorySystem(memory_config)

            self.logger.info("✅ Dependências QUALIA configuradas com sucesso")

        except Exception as e:
            self.logger.error(f"❌ Erro ao configurar dependências QUALIA: {e}")
            self.logger.warning("🔄 Usando dependências mock...")
            self._setup_mock_dependencies()

    def _setup_mock_dependencies(self):
        """Configura dependências mock quando QUALIA não está disponível."""

        class MockRiskManager:
            def __init__(self, initial_capital, risk_per_trade_pct):
                self.initial_capital = initial_capital
                self.risk_per_trade_pct = risk_per_trade_pct

            def validate_position(self, *args, **kwargs):
                return True

            def calculate_position_size(self, *args, **kwargs):
                return 0.001  # Ultra-conservativo

            def update_capital(self, *args, **kwargs):
                return True

        class MockMemorySystem:
            def __init__(self):
                self.qpm = None

        self.risk_manager = MockRiskManager(
            self.config.initial_capital,
            self.config.risk_per_trade_pct
        )
        self.memory_system = MockMemorySystem()

        self.logger.info("✅ Dependências mock configuradas")

    def load_market_data(self, file_path: str, symbol: str) -> pd.DataFrame:
        """Carrega e prepara dados de mercado."""
        self.logger.info(f"📊 Carregando dados para {symbol}...")

        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('datetime', inplace=True)

        # Adiciona colunas derivadas necessárias
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        df['volatility'] = df['returns'].rolling(24).std()

        self.logger.info(f"✅ Dados carregados: {len(df)} registros de {df.index.min()} até {df.index.max()}")
        return df

    def create_strategy_instance(self, strategy_name: str, symbol: str, timeframe: str = "1h") -> Any:
        """Cria instância de estratégia usando StrategyFactory."""
        try:
            alias = self.available_strategies.get(strategy_name)
            if not alias:
                raise ValueError(f"Estratégia {strategy_name} não encontrada")

            self.logger.info(f"🔧 Criando estratégia {strategy_name} (alias: {alias})...")

            # Contexto completo para a estratégia com dependências configuradas
            context = {
                "symbol": symbol,
                "timeframe": timeframe,
                "qpm_instance": self.memory_system.qpm if self.memory_system else None,
                "trader_mode": "backtest",
                "risk_manager": self.risk_manager,
                "memory_system": self.memory_system,
                "backtest_mode": True
            }

            # Tenta criar via StrategyFactory
            try:
                if QUALIA_IMPORTS_AVAILABLE:
                    strategy = StrategyFactory.create_strategy(
                        alias=alias,
                        context=context
                    )

                    # Configura dependências na estratégia se necessário
                    if hasattr(strategy, 'shared_context') and strategy.shared_context:
                        if hasattr(strategy.shared_context, 'risk_manager'):
                            strategy.shared_context.risk_manager = self.risk_manager
                        if hasattr(strategy.shared_context, 'memory_system'):
                            strategy.shared_context.memory_system = self.memory_system

                    self.logger.info(f"✅ Estratégia {strategy_name} criada com sucesso")
                    return strategy
                else:
                    return self._create_fallback_strategy(strategy_name, symbol, timeframe)

            except Exception as factory_error:
                self.logger.warning(f"⚠️  StrategyFactory falhou: {factory_error}")
                # Fallback: criação manual básica
                return self._create_fallback_strategy(strategy_name, symbol, timeframe)

        except Exception as e:
            self.logger.error(f"❌ Erro ao criar estratégia {strategy_name}: {e}")
            return None

    def _create_fallback_strategy(self, strategy_name: str, symbol: str, timeframe: str):
        """Cria estratégia usando fallback manual."""
        self.logger.info(f"🔄 Usando fallback para {strategy_name}...")

        # Implementação básica de fallback
        class FallbackStrategy:
            def __init__(self, name, symbol, timeframe):
                self.name = name
                self.symbol = symbol
                self.timeframe = timeframe

            def analyze_market(self, data):
                # Estratégia buy-and-hold simples como fallback
                return {
                    'signal': 1.0 if len(data) > 100 else 0.0,
                    'confidence': 0.5,
                    'position_size': 0.1
                }

        return FallbackStrategy(strategy_name, symbol, timeframe)

    def simulate_trading(self, strategy: Any, market_data: pd.DataFrame) -> Dict[str, Any]:
        """Simula trading com a estratégia fornecida."""
        self.logger.info(f"🎯 Iniciando simulação para {strategy.name if hasattr(strategy, 'name') else 'estratégia'}...")

        capital = self.config.initial_capital
        position = 0.0
        trades = []
        equity_curve = []

        # Período de warm-up
        warmup_period = max(self.config.min_lookback_periods, 100)

        for i in range(warmup_period, len(market_data)):
            current_slice = market_data.iloc[:i+1]
            current_price = current_slice['close'].iloc[-1]

            try:
                # Análise da estratégia
                analysis = strategy.analyze_market(current_slice)

                if isinstance(analysis, dict):
                    signal = analysis.get('signal', 0.0)
                    confidence = analysis.get('confidence', 0.5)
                    position_size = analysis.get('position_size', 0.1)
                else:
                    # Fallback se análise não retornar dict
                    signal = 0.0
                    confidence = 0.5
                    position_size = 0.1

                # Lógica de trading simplificada
                # Garante que signal é numérico
                try:
                    signal = float(signal) if signal is not None else 0.0
                except (ValueError, TypeError):
                    signal = 0.0

                if abs(signal) > 0.1:  # Threshold mínimo
                    target_position = signal * position_size * capital / current_price

                    if abs(target_position - position) > 0.001:  # Mudança significativa
                        trade_size = target_position - position

                        # Aplica custos de transação
                        cost = abs(trade_size * current_price) * (
                            self.config.commission_rate + self.config.slippage_rate
                        )
                        capital -= cost

                        # Registra trade
                        trades.append({
                            'timestamp': current_slice.index[-1],
                            'price': current_price,
                            'size': trade_size,
                            'position_before': position,
                            'position_after': target_position,
                            'capital': capital,
                            'signal': signal,
                            'confidence': confidence
                        })

                        position = target_position

                # Atualiza equity
                portfolio_value = capital + (position * current_price)
                equity_curve.append({
                    'timestamp': current_slice.index[-1],
                    'capital': capital,
                    'position_value': position * current_price,
                    'total_value': portfolio_value,
                    'returns': (portfolio_value - self.config.initial_capital) / self.config.initial_capital
                })

            except Exception as e:
                self.logger.warning(f"⚠️  Erro na simulação no índice {i}: {e}")
                continue

        # Fecha posição final
        if position != 0:
            final_price = market_data['close'].iloc[-1]
            capital += position * final_price
            trades.append({
                'timestamp': market_data.index[-1],
                'price': final_price,
                'size': -position,
                'position_before': position,
                'position_after': 0,
                'capital': capital,
                'signal': 0,
                'confidence': 0
            })

        return {
            'trades': trades,
            'equity_curve': equity_curve,
            'final_capital': capital,
            'total_trades': len(trades)
        }

    def calculate_performance_metrics(self, simulation_results: Dict[str, Any],
                                    strategy_name: str, symbol: str) -> PerformanceMetrics:
        """Calcula métricas de performance completas."""

        equity_curve = simulation_results['equity_curve']
        trades = simulation_results['trades']

        if not equity_curve:
            # Retorna métricas zeradas se não há dados
            return PerformanceMetrics(
                strategy_name=strategy_name,
                symbol=symbol,
                total_return=0.0,
                annualized_return=0.0,
                volatility=0.0,
                sharpe_ratio=0.0,
                sortino_ratio=0.0,
                max_drawdown=0.0,
                cagr=0.0,
                win_rate=0.0,
                profit_factor=0.0,
                total_trades=0,
                avg_trade_duration=0.0,
                final_capital=self.config.initial_capital
            )

        # Converte para DataFrame para facilitar cálculos
        equity_df = pd.DataFrame(equity_curve)
        equity_df.set_index('timestamp', inplace=True)

        # Retornos
        returns = equity_df['returns'].values
        portfolio_returns = np.diff(equity_df['total_value'].values) / equity_df['total_value'].values[:-1]

        # Métricas básicas
        total_return = returns[-1] if len(returns) > 0 else 0.0

        # Volatilidade anualizada (assumindo dados horários)
        volatility = np.std(portfolio_returns) * np.sqrt(24 * 365) if len(portfolio_returns) > 1 else 0.0

        # Retorno anualizado
        days = (equity_df.index[-1] - equity_df.index[0]).days
        annualized_return = (1 + total_return) ** (365 / max(days, 1)) - 1 if days > 0 else 0.0

        # Sharpe Ratio
        if volatility > 0:
            sharpe_ratio = annualized_return / volatility
        else:
            sharpe_ratio = 0.0

        # Sortino Ratio (usando apenas retornos negativos)
        negative_returns = portfolio_returns[portfolio_returns < 0]
        downside_deviation = np.std(negative_returns) * np.sqrt(24 * 365) if len(negative_returns) > 0 else 0.0
        sortino_ratio = annualized_return / downside_deviation if downside_deviation > 0 else 0.0

        # Maximum Drawdown
        cumulative = (1 + equity_df['returns']).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdowns = (cumulative - rolling_max) / rolling_max
        max_drawdown = abs(drawdowns.min()) if len(drawdowns) > 0 else 0.0

        # CAGR
        if days > 0:
            cagr = (simulation_results['final_capital'] / self.config.initial_capital) ** (365 / days) - 1
        else:
            cagr = 0.0

        # Métricas de trades
        if trades:
            profitable_trades = [t for t in trades if t.get('size', 0) * (t.get('price', 0) - trades[0].get('price', 0)) > 0]
            win_rate = len(profitable_trades) / len(trades) if trades else 0.0

            # Profit Factor (gross profit / gross loss)
            profits = [max(0, t.get('size', 0) * t.get('price', 0)) for t in trades]
            losses = [abs(min(0, t.get('size', 0) * t.get('price', 0))) for t in trades]
            total_profit = sum(profits)
            total_loss = sum(losses)
            profit_factor = total_profit / total_loss if total_loss > 0 else 0.0

            # Duração média dos trades (simplificada)
            avg_trade_duration = 24.0  # Assumindo 24h como média
        else:
            win_rate = 0.0
            profit_factor = 0.0
            avg_trade_duration = 0.0

        # Métricas quânticas (placeholder - seriam calculadas pelas estratégias específicas)
        quantum_advantage = 0.0
        cross_modal_coherence = 0.0

        return PerformanceMetrics(
            strategy_name=strategy_name,
            symbol=symbol,
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            cagr=cagr,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_trades=len(trades),
            avg_trade_duration=avg_trade_duration,
            quantum_advantage=quantum_advantage,
            cross_modal_coherence=cross_modal_coherence,
            final_capital=simulation_results['final_capital']
        )

    def run_comprehensive_backtest(self, data_files: Dict[str, str]) -> Dict[str, Any]:
        """Executa backtest completo para todas as estratégias e ativos."""

        self.logger.info("🚀 Iniciando análise comparativa completa...")
        self.logger.info(f"📊 Estratégias: {list(self.available_strategies.keys())}")
        self.logger.info(f"💰 Ativos: {list(data_files.keys())}")

        all_results = {}
        summary_metrics = []

        # Para cada ativo
        for symbol, file_path in data_files.items():
            self.logger.info(f"\n🎯 Processando {symbol}...")

            # Carrega dados
            try:
                market_data = self.load_market_data(file_path, symbol)
            except Exception as e:
                self.logger.error(f"❌ Erro ao carregar dados para {symbol}: {e}")
                continue

            asset_results = {}

            # Para cada estratégia
            for strategy_name in self.available_strategies.keys():
                self.logger.info(f"\n  🔧 Testando {strategy_name} em {symbol}...")

                try:
                    # Cria estratégia
                    strategy = self.create_strategy_instance(strategy_name, symbol)
                    if not strategy:
                        self.logger.error(f"❌ Falha ao criar {strategy_name}")
                        continue

                    # Executa simulação
                    start_time = time.time()
                    simulation_results = self.simulate_trading(strategy, market_data)
                    execution_time = time.time() - start_time

                    # Calcula métricas
                    metrics = self.calculate_performance_metrics(
                        simulation_results, strategy_name, symbol
                    )

                    # Armazena resultados
                    asset_results[strategy_name] = {
                        'metrics': metrics,
                        'simulation_results': simulation_results,
                        'execution_time': execution_time
                    }

                    summary_metrics.append(metrics)

                    self.logger.info(f"  ✅ {strategy_name}: Retorno={metrics.total_return:.2%}, "
                                   f"Sharpe={metrics.sharpe_ratio:.2f}, "
                                   f"Drawdown={metrics.max_drawdown:.2%}")

                except Exception as e:
                    self.logger.error(f"  ❌ Erro em {strategy_name}: {e}")
                    self.logger.debug(f"Traceback: {traceback.format_exc()}")
                    continue

            all_results[symbol] = asset_results

        # Análise comparativa
        comparative_analysis = self._generate_comparative_analysis(summary_metrics)

        final_results = {
            'individual_results': all_results,
            'comparative_analysis': comparative_analysis,
            'config': asdict(self.config),
            'execution_timestamp': datetime.now().isoformat(),
            'total_combinations': len(data_files) * len(self.available_strategies)
        }

        self.results = final_results
        self.logger.info("🎉 Análise comparativa concluída!")

        return final_results

    def _generate_comparative_analysis(self, metrics_list: List[PerformanceMetrics]) -> Dict[str, Any]:
        """Gera análise comparativa dos resultados."""

        if not metrics_list:
            return {"error": "Nenhuma métrica disponível para análise"}

        # Converte para DataFrame para facilitar análise
        df = pd.DataFrame([m.to_dict() for m in metrics_list])

        # Rankings por métrica
        rankings = {}
        key_metrics = ['sharpe_ratio', 'total_return', 'max_drawdown', 'cagr', 'win_rate']

        for metric in key_metrics:
            if metric in df.columns:
                if metric == 'max_drawdown':  # Menor é melhor
                    rankings[metric] = df.nsmallest(10, metric)[['strategy_name', 'symbol', metric]].to_dict('records')
                else:  # Maior é melhor
                    rankings[metric] = df.nlargest(10, metric)[['strategy_name', 'symbol', metric]].to_dict('records')

        # Estatísticas por estratégia
        strategy_stats = df.groupby('strategy_name').agg({
            'total_return': ['mean', 'std', 'min', 'max'],
            'sharpe_ratio': ['mean', 'std', 'min', 'max'],
            'max_drawdown': ['mean', 'std', 'min', 'max'],
            'win_rate': ['mean', 'std', 'min', 'max']
        }).round(4)

        # Melhor estratégia por ativo
        best_by_asset = {}
        for symbol in df['symbol'].unique():
            symbol_data = df[df['symbol'] == symbol]
            best_idx = symbol_data['sharpe_ratio'].idxmax()
            best_by_asset[symbol] = {
                'strategy': symbol_data.loc[best_idx, 'strategy_name'],
                'sharpe_ratio': symbol_data.loc[best_idx, 'sharpe_ratio'],
                'total_return': symbol_data.loc[best_idx, 'total_return']
            }

        # Estratégia mais consistente (menor desvio padrão do Sharpe)
        consistency_analysis = df.groupby('strategy_name')['sharpe_ratio'].agg(['mean', 'std']).round(4)
        consistency_analysis['consistency_score'] = consistency_analysis['mean'] / (consistency_analysis['std'] + 0.001)
        most_consistent = consistency_analysis.sort_values('consistency_score', ascending=False).index[0]

        return {
            'rankings': rankings,
            'strategy_statistics': strategy_stats.to_dict(),
            'best_by_asset': best_by_asset,
            'most_consistent_strategy': most_consistent,
            'total_strategies_tested': df['strategy_name'].nunique(),
            'total_assets_tested': df['symbol'].nunique(),
            'average_performance': {
                'sharpe_ratio': df['sharpe_ratio'].mean(),
                'total_return': df['total_return'].mean(),
                'max_drawdown': df['max_drawdown'].mean(),
                'win_rate': df['win_rate'].mean()
            }
        }

    def save_results(self, output_dir: str = "analysis/results"):
        """Salva resultados em arquivos JSON e CSV."""

        if not self.results:
            self.logger.warning("⚠️  Nenhum resultado para salvar")
            return

        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Salva resultados completos em JSON
        json_file = output_path / f"backtest_results_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, default=str, ensure_ascii=False)

        # Extrai métricas para CSV
        metrics_data = []
        for symbol, strategies in self.results['individual_results'].items():
            for strategy_name, result in strategies.items():
                metrics = result['metrics']
                metrics_dict = metrics.to_dict()
                metrics_dict['execution_time'] = result['execution_time']
                metrics_data.append(metrics_dict)

        if metrics_data:
            csv_file = output_path / f"performance_metrics_{timestamp}.csv"
            pd.DataFrame(metrics_data).to_csv(csv_file, index=False)

        self.logger.info(f"📁 Resultados salvos em:")
        self.logger.info(f"   JSON: {json_file}")
        self.logger.info(f"   CSV: {csv_file}")

    def generate_report(self) -> str:
        """Gera relatório textual dos resultados."""

        if not self.results:
            return "❌ Nenhum resultado disponível para relatório"

        report = []
        report.append("🌌 QUALIA - RELATÓRIO DE ANÁLISE COMPARATIVA DE ESTRATÉGIAS")
        report.append("=" * 80)

        # Informações gerais
        report.append(f"\n📊 RESUMO EXECUTIVO")
        report.append(f"   • Data/Hora: {self.results['execution_timestamp']}")
        report.append(f"   • Combinações testadas: {self.results['total_combinations']}")
        report.append(f"   • Capital inicial: ${self.config.initial_capital:,.2f}")
        report.append(f"   • Risk per trade: {self.config.risk_per_trade_pct}%")

        # Análise comparativa
        comp_analysis = self.results['comparative_analysis']

        report.append(f"\n🏆 RANKINGS POR MÉTRICA")
        report.append("-" * 40)

        # Top 3 por Sharpe Ratio
        if 'sharpe_ratio' in comp_analysis['rankings']:
            report.append(f"\n📈 TOP 3 - SHARPE RATIO:")
            for i, entry in enumerate(comp_analysis['rankings']['sharpe_ratio'][:3], 1):
                report.append(f"   {i}. {entry['strategy_name']} ({entry['symbol']}): {entry['sharpe_ratio']:.3f}")

        # Top 3 por Retorno Total
        if 'total_return' in comp_analysis['rankings']:
            report.append(f"\n💰 TOP 3 - RETORNO TOTAL:")
            for i, entry in enumerate(comp_analysis['rankings']['total_return'][:3], 1):
                report.append(f"   {i}. {entry['strategy_name']} ({entry['symbol']}): {entry['total_return']:.2%}")

        # Menor Drawdown
        if 'max_drawdown' in comp_analysis['rankings']:
            report.append(f"\n🛡️  TOP 3 - MENOR DRAWDOWN:")
            for i, entry in enumerate(comp_analysis['rankings']['max_drawdown'][:3], 1):
                report.append(f"   {i}. {entry['strategy_name']} ({entry['symbol']}): {entry['max_drawdown']:.2%}")

        # Melhor por ativo
        report.append(f"\n🎯 MELHOR ESTRATÉGIA POR ATIVO")
        report.append("-" * 40)
        for asset, best in comp_analysis['best_by_asset'].items():
            report.append(f"   {asset}: {best['strategy']} (Sharpe: {best['sharpe_ratio']:.3f}, Retorno: {best['total_return']:.2%})")

        # Estratégia mais consistente
        report.append(f"\n🔄 ANÁLISE DE CONSISTÊNCIA")
        report.append("-" * 40)
        report.append(f"   Estratégia mais consistente: {comp_analysis['most_consistent_strategy']}")

        # Performance média
        avg_perf = comp_analysis['average_performance']
        report.append(f"\n📊 PERFORMANCE MÉDIA GERAL")
        report.append("-" * 40)
        report.append(f"   • Sharpe Ratio médio: {avg_perf['sharpe_ratio']:.3f}")
        report.append(f"   • Retorno médio: {avg_perf['total_return']:.2%}")
        report.append(f"   • Drawdown médio: {avg_perf['max_drawdown']:.2%}")
        report.append(f"   • Win Rate médio: {avg_perf['win_rate']:.2%}")

        # Recomendações
        report.append(f"\n💡 RECOMENDAÇÕES")
        report.append("-" * 40)

        best_overall = comp_analysis['rankings']['sharpe_ratio'][0] if comp_analysis['rankings'].get('sharpe_ratio') else None
        if best_overall:
            report.append(f"   🥇 Melhor performance geral: {best_overall['strategy_name']} em {best_overall['symbol']}")

        report.append(f"   🔄 Para consistência: {comp_analysis['most_consistent_strategy']}")
        report.append(f"   📈 Estratégias testadas: {comp_analysis['total_strategies_tested']}")
        report.append(f"   💎 Ativos analisados: {comp_analysis['total_assets_tested']}")

        report.append(f"\n" + "=" * 80)
        report.append("🌌 Análise gerada pelo sistema QUALIA - YAA Implementation")

        return "\n".join(report)

# Função principal para execução
def main():
    """Função principal para executar análise comparativa."""

    print("🌌 QUALIA - Framework Unificado de Backtesting")
    print("=" * 60)

    # Configuração
    config = BacktestConfig(
        initial_capital=100000.0,
        risk_per_trade_pct=2.0,
        commission_rate=0.001,
        slippage_rate=0.0005
    )

    # Arquivos de dados
    data_files = {
        'BTC_USDT': 'data/historical/BTC_USDT_1h.csv',
        'ETH_USDT': 'data/historical/ETH_USDT_1h.csv',
        'SOL_USDT': 'data/historical/SOL_USDT_1h.csv'
    }

    # Executa análise
    framework = UnifiedBacktestingFramework(config)
    results = framework.run_comprehensive_backtest(data_files)

    # Salva resultados
    framework.save_results()

    # Gera e exibe relatório
    report = framework.generate_report()
    print(report)

    return results

if __name__ == "__main__":
    results = main()