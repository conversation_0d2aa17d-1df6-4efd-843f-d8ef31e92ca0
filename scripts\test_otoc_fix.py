#!/usr/bin/env python3
"""
Teste da Correção do OTOC
Valida que a correção introduz diversidade real no cálculo do OTOC.
"""

import sys
import os

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

import numpy as np


class MinimalUniverse:
    """Classe reduzida contendo apenas o cálculo de OTOC."""

    def __init__(self, n_qubits: int, otoc_frequency: int = 1) -> None:
        self.n_qubits = n_qubits
        self.otoc_frequency = otoc_frequency
        self.run_count = 0
        self._last_otoc_value: float | None = None
        self._otoc_constant_count = 0
        self._otoc_stagnant = False
        self.invalid_metric = False

    def _check_otoc_constancy(self, new_value: float | None) -> bool:
        if new_value is None:
            self._last_otoc_value = None
            self._otoc_constant_count = 0
            self._otoc_stagnant = False
            self.invalid_metric = False
            return False

        if self._last_otoc_value is None or not np.isclose(
            new_value, self._last_otoc_value
        ):
            self._otoc_constant_count = 1
            stagnation = False
            self.invalid_metric = False
        else:
            self._otoc_constant_count += 1
            stagnation = self._otoc_constant_count >= 2
            if self._otoc_constant_count >= 3:
                self.invalid_metric = True

        self._last_otoc_value = new_value
        self._otoc_stagnant = stagnation
        return stagnation

    def calculate_otoc(
        self, statevector, time_step: float, n_qubits: int, target_qubits=None
    ) -> float | None:
        if target_qubits is None:
            target_qubits = [0]

        if self.run_count % self.otoc_frequency != 0:
            return self._last_otoc_value

        if self._otoc_stagnant:
            return self._last_otoc_value

        from qiskit.quantum_info import Statevector  # type: ignore

        if isinstance(statevector, Statevector) and statevector.num_qubits != n_qubits:
            return 0.5

        operator_pairs = [("x", "z"), ("y", "z"), ("x", "y"), ("z", "x")]
        pair_index = int((self.run_count + time_step) % len(operator_pairs))
        op_w_type, op_v_type = operator_pairs[pair_index]

        ops = {
            "x": np.array([[0, 1], [1, 0]], dtype=complex),
            "y": np.array([[0, -1j], [1j, 0]], dtype=complex),
            "z": np.array([[1, 0], [0, -1]], dtype=complex),
        }

        def rx(theta: float) -> np.ndarray:
            return np.array(
                [
                    [np.cos(theta / 2), -1j * np.sin(theta / 2)],
                    [-1j * np.sin(theta / 2), np.cos(theta / 2)],
                ],
                dtype=complex,
            )

        def tensor_op(single_op, target, total):
            mats = [single_op if i == target else np.eye(2) for i in range(total)]
            result = mats[0]
            for m in mats[1:]:
                result = np.kron(result, m)
            return result

        W_ext = np.eye(2**n_qubits, dtype=complex)
        V_ext = np.eye(2**n_qubits, dtype=complex)
        for q_idx in target_qubits:
            if q_idx < n_qubits:
                W_ext = W_ext @ tensor_op(ops[op_w_type], q_idx, n_qubits)
                V_ext = V_ext @ tensor_op(ops[op_v_type], q_idx, n_qubits)

        theta = (self.run_count + time_step) * np.pi / 4
        W_t_ext = tensor_op(rx(theta), target_qubits[0], n_qubits) @ W_ext

        commutator = W_t_ext @ V_ext - V_ext @ W_t_ext
        otoc_matrix = commutator.conj().T @ commutator

        if isinstance(statevector, Statevector):
            otoc_val = statevector.expectation_value(otoc_matrix)
        else:
            otoc_val = np.trace(statevector @ otoc_matrix)

        max_theoretical = 4.0 * len(target_qubits)
        otoc_norm = float(np.abs(otoc_val) / max_theoretical)
        self._check_otoc_constancy(otoc_norm)
        return None if self.invalid_metric else otoc_norm


def test_otoc_diversity():
    """Testa se a correção do OTOC introduz diversidade real."""
    print("🧪 TESTE: Diversidade do OTOC Corrigido")
    print("=" * 50)

    # Criar universo quântico mínimo apenas com cálculo de OTOC
    universe = MinimalUniverse(n_qubits=4, otoc_frequency=1)

    # Simular múltiplos ciclos para ver variação do OTOC
    otoc_values = []

    print("\nCiclo\tOTOC\t\tOperadores\tTemporal Factor")
    print("-" * 55)

    rng = np.random.default_rng(42)

    for cycle in range(10):
        universe.run_count = cycle

        try:
            # Gerar statevector aleatório para simulação
            random_phases = rng.uniform(0, 2 * np.pi, size=2**4)
            state = np.exp(1j * random_phases)
            state /= np.linalg.norm(state)
            rho = np.outer(state, state.conj())

            otoc_val = universe.calculate_otoc(
                statevector=rho,
                time_step=(cycle + 1) * 0.5,
                n_qubits=4,
                target_qubits=[0],
            )

            if otoc_val is not None:
                otoc_values.append(otoc_val)

                # Determinar qual par de operadores foi usado
                operator_pairs = [("X", "Z"), ("Y", "Z"), ("X", "Y"), ("Z", "X")]
                pair_index = (cycle + cycle) % len(operator_pairs)
                ops = operator_pairs[pair_index]

                temporal_factor = (cycle % 10) + 1

                print(
                    f"{cycle}\t{otoc_val:.6f}\t{ops[0]}/{ops[1]}\t\t{temporal_factor}"
                )
            else:
                print(f"{cycle}\tNone\t\t-\t\t-")

        except Exception as e:
            print(f"{cycle}\tERROR: {str(e)[:30]}...")

    # Análise da diversidade
    print("\n📊 ANÁLISE DA DIVERSIDADE:")
    print(f"Valores únicos de OTOC: {len(set(otoc_values))}")
    print(f"Variância: {np.var(otoc_values):.6f}")
    print(f"Desvio padrão: {np.std(otoc_values):.6f}")
    print(f"Amplitude (max-min): {max(otoc_values) - min(otoc_values):.6f}")

    # Verificar se há diversidade real
    if len(set(otoc_values)) > 1:
        print("✅ SUCESSO: OTOC mostra diversidade real!")
    else:
        print("❌ FALHA: OTOC ainda está estagnado")

    return otoc_values


if __name__ == "__main__":
    test_otoc_diversity()
