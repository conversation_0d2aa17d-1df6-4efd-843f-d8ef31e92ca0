#!/usr/bin/env python3
"""
Setup do Ambiente de Backtesting QUALIA
YAA IMPLEMENTATION: Configuração adequada das dependências
"""

import os
import sys
from pathlib import Path

def setup_environment():
    """Configura o ambiente para backtesting."""
    
    # Adiciona paths necessários
    root_path = Path(__file__).parent.parent
    sys.path.insert(0, str(root_path))
    sys.path.insert(0, str(root_path / "src"))
    
    # Configura variáveis de ambiente para QUALIA
    os.environ.setdefault("QUALIA_ENV", "backtest")
    os.environ.setdefault("QUALIA_LOG_LEVEL", "INFO")
    os.environ.setdefault("QUALIA_EXPERIMENTAL", "0")  # Desabilita recursos experimentais
    
    # Configurações específicas para backtesting
    os.environ.setdefault("QUALIA_BACKTEST_MODE", "1")
    os.environ.setdefault("QUALIA_DISABLE_LIVE_DATA", "1")
    os.environ.setdefault("QUALIA_DISABLE_TRADING", "1")
    
    print("🔧 Ambiente de backtesting configurado")
    print(f"   • Root path: {root_path}")
    print(f"   • Python paths adicionados")
    print(f"   • Variáveis de ambiente configuradas")

def create_mock_config():
    """Cria configuração mock para backtesting."""
    
    config = {
        "risk_management": {
            "risk_profile": "balanced",
            "risk_per_trade_pct": 2.0,
            "max_position_size_pct": 10.0,
            "max_drawdown_pct": 20.0,
            "max_open_positions": 5
        },
        "memory": {
            "qpm_memory_file": None,
            "experimental_enabled": False,
            "holo_max_items": 1000,
            "holo_half_life": 3600.0
        },
        "trading": {
            "base_currency": "USDT",
            "enable_live_data": False,
            "backtest_mode": True
        },
        "logging": {
            "level": "INFO",
            "disable_external_logs": True
        }
    }
    
    return config

def patch_qualia_imports():
    """Aplica patches necessários para importações do QUALIA."""
    
    try:
        # Tenta importar e configurar módulos críticos
        from qualia.utils.logger import get_logger
        
        # Configura logger para backtesting
        logger = get_logger("backtest_setup")
        logger.info("🔧 Configurando ambiente QUALIA para backtesting...")
        
        # Patches específicos se necessário
        import qualia.memory.experience_replay
        
        # Monkey patch para ExperienceReplay não exigir RiskManager em alguns métodos
        original_require_risk_manager = qualia.memory.experience_replay.ExperienceReplay._require_risk_manager
        
        def patched_require_risk_manager(self):
            """Versão patcheada que não falha em modo backtest."""
            if hasattr(self, 'risk_manager') and self.risk_manager is not None:
                return
            if os.environ.get("QUALIA_BACKTEST_MODE") == "1":
                # Em modo backtest, cria um mock temporário se necessário
                if not hasattr(self, '_mock_risk_manager'):
                    class MockRiskManager:
                        def update_capital(self, *args, **kwargs):
                            return True
                    self._mock_risk_manager = MockRiskManager()
                    self.risk_manager = self._mock_risk_manager
                return
            # Comportamento original para outros casos
            original_require_risk_manager(self)
        
        qualia.memory.experience_replay.ExperienceReplay._require_risk_manager = patched_require_risk_manager
        
        logger.info("✅ Patches aplicados com sucesso")
        return True
        
    except ImportError as e:
        print(f"⚠️  Não foi possível aplicar patches QUALIA: {e}")
        return False

def main():
    """Função principal de setup."""
    
    print("🌌 QUALIA - Setup do Ambiente de Backtesting")
    print("=" * 60)
    
    # 1. Configura ambiente básico
    setup_environment()
    
    # 2. Aplica patches necessários
    patches_applied = patch_qualia_imports()
    
    # 3. Cria configuração
    config = create_mock_config()
    
    print("\n📋 Configuração criada:")
    for section, values in config.items():
        print(f"   • {section}: {len(values)} parâmetros")
    
    print(f"\n✅ Setup concluído (patches: {'OK' if patches_applied else 'SKIP'})")
    
    return config

if __name__ == "__main__":
    config = main()
