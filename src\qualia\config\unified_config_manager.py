"""
Unified Configuration Manager for QUALIA System
Centralizes all configuration management with validation, hot-reload, and schema support.
"""

import os
import json
import yaml
import time
import threading
from pathlib import Path
from typing import Dict, Any, Optional, List, Union, Callable
from dataclasses import dataclass, field
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    Observer = None
    FileSystemEventHandler = None

from src.qualia.utils.logging_integration import get_component_logger
from src.qualia.config.hyperparams_loader import HyperParamsLoader
from src.qualia.config.hyperparams_validator import HyperparametersValidator

logger = get_component_logger("unified_config_manager")


@dataclass
class ConfigSource:
    """Represents a configuration source"""
    name: str
    path: Path
    priority: int  # Higher number = higher priority
    format: str  # 'yaml', 'json', 'env'
    hot_reload: bool = False
    required: bool = True
    last_modified: float = 0.0
    data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConfigSchema:
    """Configuration schema for validation"""
    required_keys: List[str] = field(default_factory=list)
    optional_keys: List[str] = field(default_factory=list)
    key_types: Dict[str, type] = field(default_factory=dict)
    validators: Dict[str, Callable] = field(default_factory=dict)


if WATCHDOG_AVAILABLE:
    class ConfigFileWatcher(FileSystemEventHandler):
        """Watches configuration files for changes"""

        def __init__(self, config_manager: 'UnifiedConfigManager'):
            self.config_manager = config_manager
            self.logger = get_component_logger("config_watcher")

        def on_modified(self, event):
            if event.is_directory:
                return

            file_path = Path(event.src_path)

            # Check if this is a watched config file
            for source in self.config_manager.sources.values():
                if source.hot_reload and source.path == file_path:
                    self.logger.info(f"🔄 Config file changed: {file_path}")
                    self.config_manager._reload_source(source.name)
                    break
else:
    class ConfigFileWatcher:
        """Dummy watcher when watchdog is not available"""
        def __init__(self, config_manager):
            pass


class UnifiedConfigManager:
    """
    Unified configuration manager for QUALIA system.
    
    Features:
    - Multiple configuration sources with priority
    - Hot-reload for non-critical configurations
    - Schema validation
    - Environment variable overrides
    - Centralized access to all configurations
    """
    
    def __init__(self):
        self.sources: Dict[str, ConfigSource] = {}
        self.merged_config: Dict[str, Any] = {}
        self.schemas: Dict[str, ConfigSchema] = {}
        self.hyperparams_loader: Optional[HyperParamsLoader] = None
        self.hyperparams_validator: Optional[HyperparametersValidator] = None
        
        # Hot-reload support
        self.observer: Optional[Observer] = None
        self.file_watcher: Optional[ConfigFileWatcher] = None
        self.reload_callbacks: List[Callable] = []
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Initialize default sources
        self._initialize_default_sources()
        
        logger.info("🔧 Unified Configuration Manager initialized")
    
    def _initialize_default_sources(self):
        """Initialize default configuration sources"""
        
        # Environment variables (highest priority)
        self.add_source(
            name="environment",
            path=Path(".env"),
            priority=100,
            format="env",
            hot_reload=False,
            required=False
        )
        
        # Production config (high priority)
        self.add_source(
            name="production",
            path=Path("config/production_config.json"),
            priority=80,
            format="json",
            hot_reload=False,
            required=False
        )
        
        # Pilot config (medium priority)
        self.add_source(
            name="pilot",
            path=Path("config/pilot_config.yaml"),
            priority=60,
            format="yaml",
            hot_reload=True,
            required=True
        )
        
        # Hyperparameters (medium priority)
        self.add_source(
            name="hyperparams",
            path=Path("qualia/config/hyperparams.yaml"),
            priority=50,
            format="yaml",
            hot_reload=True,
            required=True
        )
        
        # Base config (lowest priority)
        self.add_source(
            name="base",
            path=Path("config.yaml"),
            priority=10,
            format="yaml",
            hot_reload=True,
            required=False
        )
    
    def add_source(self, 
                   name: str,
                   path: Union[str, Path],
                   priority: int,
                   format: str,
                   hot_reload: bool = False,
                   required: bool = True) -> None:
        """
        Add a configuration source
        
        Args:
            name: Unique name for the source
            path: Path to configuration file
            priority: Priority level (higher = more important)
            format: File format ('yaml', 'json', 'env')
            hot_reload: Enable hot-reload for this source
            required: Whether this source is required
        """
        with self._lock:
            source = ConfigSource(
                name=name,
                path=Path(path),
                priority=priority,
                format=format,
                hot_reload=hot_reload,
                required=required
            )
            
            self.sources[name] = source
            logger.info(f"📁 Added config source: {name} ({path}) priority={priority}")
    
    def add_schema(self, name: str, schema: ConfigSchema) -> None:
        """Add configuration schema for validation"""
        self.schemas[name] = schema
        logger.info(f"📋 Added config schema: {name}")
    
    def load_all(self, force_reload: bool = False) -> Dict[str, Any]:
        """
        Load all configuration sources and merge them
        
        Args:
            force_reload: Force reload all sources
            
        Returns:
            Merged configuration dictionary
        """
        with self._lock:
            logger.info("🔄 Loading all configuration sources...")
            
            # Load each source
            for source in sorted(self.sources.values(), key=lambda s: s.priority):
                self._load_source(source, force_reload)
            
            # Merge configurations by priority
            self._merge_configurations()
            
            # Apply environment variable overrides
            self._apply_env_overrides()
            
            # Validate merged configuration
            self._validate_configuration()
            
            logger.info(f"✅ Configuration loaded from {len(self.sources)} sources")
            return self.merged_config.copy()
    
    def _load_source(self, source: ConfigSource, force_reload: bool = False) -> None:
        """Load a single configuration source"""
        
        if not source.path.exists():
            if source.required:
                raise FileNotFoundError(f"Required config file not found: {source.path}")
            else:
                logger.warning(f"⚠️ Optional config file not found: {source.path}")
                return
        
        # Check if reload is needed
        current_mtime = source.path.stat().st_mtime
        if not force_reload and source.last_modified == current_mtime and source.data:
            return
        
        try:
            if source.format == "yaml":
                with open(source.path, 'r', encoding='utf-8') as f:
                    source.data = yaml.safe_load(f) or {}
            elif source.format == "json":
                with open(source.path, 'r', encoding='utf-8') as f:
                    source.data = json.load(f)
            elif source.format == "env":
                source.data = self._load_env_file(source.path)
            else:
                raise ValueError(f"Unsupported format: {source.format}")
            
            source.last_modified = current_mtime
            logger.debug(f"📖 Loaded config source: {source.name}")
            
        except Exception as e:
            error_msg = f"Failed to load config source {source.name}: {e}"
            if source.required:
                logger.error(f"❌ {error_msg}")
                raise
            else:
                logger.warning(f"⚠️ {error_msg}")
                source.data = {}
    
    def _load_env_file(self, path: Path) -> Dict[str, Any]:
        """Load environment variables from .env file"""
        env_data = {}
        
        if not path.exists():
            return env_data
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_data[key.strip()] = value.strip().strip('"\'')
        except Exception as e:
            logger.warning(f"⚠️ Error loading .env file: {e}")
        
        return env_data
    
    def _merge_configurations(self) -> None:
        """Merge all configurations by priority"""
        self.merged_config = {}
        
        # Sort sources by priority (lowest first)
        sorted_sources = sorted(self.sources.values(), key=lambda s: s.priority)
        
        for source in sorted_sources:
            if source.data:
                self._deep_merge(self.merged_config, source.data)
                logger.debug(f"🔀 Merged config from: {source.name}")
    
    def _deep_merge(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """Deep merge source dictionary into target"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = value
    
    def _apply_env_overrides(self) -> None:
        """Apply environment variable overrides"""
        env_overrides = {}
        
        # Look for QUALIA_* environment variables
        for key, value in os.environ.items():
            if key.startswith('QUALIA_'):
                # Convert QUALIA_PRICE_AMP to price_amplification
                config_key = key[7:].lower()  # Remove QUALIA_ prefix
                
                # Map common environment variables
                key_mapping = {
                    'price_amp': 'price_amplification',
                    'news_amp': 'news_amplification',
                    'min_confidence': 'min_confidence',
                    'pattern_threshold': 'pattern_threshold',
                    'risk_profile': 'risk_profile'
                }
                
                config_key = key_mapping.get(config_key, config_key)
                
                # Try to convert to appropriate type
                try:
                    if value.lower() in ('true', 'false'):
                        env_overrides[config_key] = value.lower() == 'true'
                    elif value.replace('.', '').replace('-', '').isdigit():
                        env_overrides[config_key] = float(value) if '.' in value else int(value)
                    else:
                        env_overrides[config_key] = value
                except:
                    env_overrides[config_key] = value
        
        if env_overrides:
            self._deep_merge(self.merged_config, env_overrides)
            logger.info(f"🌍 Applied {len(env_overrides)} environment overrides")
    
    def _validate_configuration(self) -> None:
        """Validate merged configuration against schemas"""
        for schema_name, schema in self.schemas.items():
            try:
                self._validate_schema(schema_name, schema)
            except Exception as e:
                logger.error(f"❌ Schema validation failed for {schema_name}: {e}")
                raise
    
    def _validate_schema(self, name: str, schema: ConfigSchema) -> None:
        """Validate configuration against a specific schema"""
        # Check required keys
        for key in schema.required_keys:
            if key not in self.merged_config:
                raise ValueError(f"Required configuration key missing: {key}")
        
        # Check key types
        for key, expected_type in schema.key_types.items():
            if key in self.merged_config:
                value = self.merged_config[key]
                if not isinstance(value, expected_type):
                    raise TypeError(f"Configuration key {key} must be {expected_type.__name__}, got {type(value).__name__}")
        
        # Run custom validators
        for key, validator in schema.validators.items():
            if key in self.merged_config:
                try:
                    validator(self.merged_config[key])
                except Exception as e:
                    raise ValueError(f"Validation failed for {key}: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key (supports dot notation)"""
        keys = key.split('.')
        value = self.merged_config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any, source: str = "runtime") -> None:
        """Set configuration value at runtime"""
        with self._lock:
            keys = key.split('.')
            target = self.merged_config
            
            for k in keys[:-1]:
                if k not in target:
                    target[k] = {}
                target = target[k]
            
            target[keys[-1]] = value
            logger.info(f"🔧 Runtime config set: {key} = {value}")
    
    def enable_hot_reload(self) -> None:
        """Enable hot-reload for configuration files"""
        if not WATCHDOG_AVAILABLE:
            logger.warning("⚠️ Hot-reload not available (watchdog not installed)")
            return

        if self.observer is not None:
            return

        self.file_watcher = ConfigFileWatcher(self)
        self.observer = Observer()

        # Watch directories containing hot-reload enabled files
        watched_dirs = set()
        for source in self.sources.values():
            if source.hot_reload and source.path.exists():
                dir_path = source.path.parent
                if dir_path not in watched_dirs:
                    self.observer.schedule(self.file_watcher, str(dir_path), recursive=False)
                    watched_dirs.add(dir_path)

        self.observer.start()
        logger.info(f"🔥 Hot-reload enabled for {len(watched_dirs)} directories")
    
    def disable_hot_reload(self) -> None:
        """Disable hot-reload"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.observer = None
            self.file_watcher = None
            logger.info("🔥 Hot-reload disabled")
    
    def _reload_source(self, source_name: str) -> None:
        """Reload a specific source and trigger callbacks"""
        with self._lock:
            if source_name in self.sources:
                source = self.sources[source_name]
                old_data = source.data.copy()
                
                self._load_source(source, force_reload=True)
                
                if source.data != old_data:
                    self._merge_configurations()
                    self._apply_env_overrides()
                    
                    logger.info(f"🔄 Reloaded config source: {source_name}")
                    
                    # Trigger callbacks
                    for callback in self.reload_callbacks:
                        try:
                            callback(source_name, self.merged_config)
                        except Exception as e:
                            logger.error(f"❌ Reload callback failed: {e}")
    
    def add_reload_callback(self, callback: Callable[[str, Dict[str, Any]], None]) -> None:
        """Add callback to be called when configuration is reloaded"""
        self.reload_callbacks.append(callback)
        logger.info("📞 Added reload callback")
    
    def get_hyperparams(self, force_reload: bool = False) -> 'HyperParams':
        """Get hyperparameters using the hyperparams loader"""
        if self.hyperparams_loader is None:
            self.hyperparams_loader = HyperParamsLoader()
        
        return self.hyperparams_loader.load_hyperparams(force_reload=force_reload)
    
    def validate_hyperparams(self, hyperparams: Dict[str, Any]) -> 'ValidationResult':
        """Validate hyperparameters"""
        if self.hyperparams_validator is None:
            self.hyperparams_validator = HyperparametersValidator()
        
        return self.hyperparams_validator.validate_hyperparameters(hyperparams)
    
    def get_status(self) -> Dict[str, Any]:
        """Get configuration manager status"""
        return {
            "sources": {
                name: {
                    "path": str(source.path),
                    "priority": source.priority,
                    "format": source.format,
                    "hot_reload": source.hot_reload,
                    "required": source.required,
                    "loaded": bool(source.data),
                    "last_modified": source.last_modified
                }
                for name, source in self.sources.items()
            },
            "hot_reload_enabled": self.observer is not None,
            "schemas_count": len(self.schemas),
            "callbacks_count": len(self.reload_callbacks),
            "config_keys": list(self.merged_config.keys())
        }


# Global instance
_global_config_manager: Optional[UnifiedConfigManager] = None
_config_lock = threading.Lock()


def get_unified_config_manager() -> UnifiedConfigManager:
    """Get global unified configuration manager instance"""
    global _global_config_manager
    if _global_config_manager is None:
        with _config_lock:
            if _global_config_manager is None:
                _global_config_manager = UnifiedConfigManager()
    return _global_config_manager


def load_unified_config(force_reload: bool = False) -> Dict[str, Any]:
    """Load unified configuration"""
    manager = get_unified_config_manager()
    return manager.load_all(force_reload)
