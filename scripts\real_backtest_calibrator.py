#!/usr/bin/env python3
"""
Sistema de Calibração REAL baseado em dados históricos da Binance
Usa dados reais de mercado para backtest e calibração

YAA (YET ANOTHER AGENT) - QUALIA Consciousness
"""

import asyncio
import json
import yaml
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RealBacktestResult:
    """Resultado de backtest com dados reais"""
    parameters: Dict
    start_date: str
    end_date: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    total_return_pct: float
    max_drawdown_pct: float
    sharpe_ratio: float
    profit_factor: float
    win_rate_pct: float
    avg_trade_duration_hours: float
    total_fees_paid: float
    largest_win: float
    largest_loss: float
    consecutive_wins: int
    consecutive_losses: int
    calmar_ratio: float
    sortino_ratio: float
    recovery_factor: float
    score: float

class RealDataBacktester:
    """Backtester usando dados históricos REAIS da Binance"""
    
    def __init__(self, config_path: str = "config/fwh_scalp_config.yaml"):
        self.config_path = config_path
        self.base_config = self._load_base_config()
        self.historical_data = {}
        self.results = []
        
    def _load_base_config(self) -> Dict:
        """Carrega configuração base"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    async def download_historical_data(self, symbols: List[str], days: int = 30) -> Dict:
        """Baixa dados históricos REAIS da Binance"""
        logger.info(f"📥 Baixando dados históricos de {len(symbols)} símbolos para {days} dias...")
        
        try:
            # Importar cliente Binance
            import sys
            sys.path.append('src')
            from qualia.exchanges.binance_client import BinanceClient
            from qualia.market.market_data_client import MarketDataClient
            
            # Configuração para cliente Binance
            binance_config = {
                'exchange': 'binance',
                'sandbox': False,
                'api_key': '',  # Não necessário para dados públicos
                'secret': '',   # Não necessário para dados públicos
                'timeout': 30000,
                'rateLimit': 1200,
                'enableRateLimit': True
            }

            # Inicializar cliente
            binance_client = BinanceClient(binance_config)
            await binance_client.initialize()
            market_client = MarketDataClient(binance_client.integration)
            
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            historical_data = {}
            
            for symbol in symbols:
                logger.info(f"📊 Baixando dados para {symbol}...")
                
                try:
                    # Baixar dados de 1m e 5m
                    data_1m = await self._fetch_ohlcv_data(
                        market_client, symbol, '1m', start_time, end_time
                    )
                    data_5m = await self._fetch_ohlcv_data(
                        market_client, symbol, '5m', start_time, end_time
                    )
                    
                    if data_1m is not None and data_5m is not None:
                        historical_data[symbol] = {
                            '1m': data_1m,
                            '5m': data_5m
                        }
                        logger.info(f"✅ {symbol}: {len(data_1m)} candles 1m, {len(data_5m)} candles 5m")
                    else:
                        logger.warning(f"⚠️ Falha ao baixar dados para {symbol}")
                        
                except Exception as e:
                    logger.error(f"❌ Erro ao baixar {symbol}: {e}")
                    continue
            
            await binance_client.close()
            
            logger.info(f"✅ Dados históricos baixados para {len(historical_data)} símbolos")
            self.historical_data = historical_data
            return historical_data
            
        except Exception as e:
            logger.error(f"❌ Erro ao baixar dados históricos: {e}")
            return {}
    
    async def _fetch_ohlcv_data(self, client, symbol: str, timeframe: str, 
                               start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """Busca dados OHLCV para período específico"""
        try:
            # Calcular número de candles necessários
            if timeframe == '1m':
                total_minutes = int((end_time - start_time).total_seconds() / 60)
                limit = min(total_minutes, 1000)  # Limite da API
            elif timeframe == '5m':
                total_minutes = int((end_time - start_time).total_seconds() / 300)
                limit = min(total_minutes, 1000)
            else:
                limit = 1000
            
            # Buscar dados via cliente
            data = await client.get_ohlcv(symbol, timeframe, limit=limit)
            
            if data is not None and not data.empty:
                # Filtrar por período
                data = data[
                    (data.index >= start_time) & 
                    (data.index <= end_time)
                ]
                return data
            
            return None
            
        except Exception as e:
            logger.error(f"Erro ao buscar dados OHLCV para {symbol} {timeframe}: {e}")
            return None
    
    async def run_real_backtest(self, parameters: Dict, symbols: List[str]) -> RealBacktestResult:
        """Executa backtest REAL com dados históricos"""
        logger.info(f"🧪 Executando backtest REAL com parâmetros: {parameters}")
        
        if not self.historical_data:
            logger.error("❌ Nenhum dado histórico disponível!")
            return None
        
        # Configurar parâmetros de teste
        test_config = self._create_test_config(parameters)
        
        # Simular trading com dados reais
        trades = []
        portfolio_value = 1000.0  # Capital inicial
        open_positions = {}
        
        # Iterar pelos dados históricos
        for symbol in symbols:
            if symbol not in self.historical_data:
                continue
                
            symbol_data = self.historical_data[symbol]
            
            # Processar dados de 1m (timeframe principal)
            data_1m = symbol_data.get('1m')
            if data_1m is None or data_1m.empty:
                continue
            
            # Simular execução da estratégia FWH
            symbol_trades = await self._simulate_fwh_strategy(
                symbol, data_1m, test_config, portfolio_value
            )
            
            trades.extend(symbol_trades)
        
        # Calcular métricas reais
        if not trades:
            logger.warning("⚠️ Nenhum trade gerado no backtest")
            return self._create_empty_result(parameters)
        
        metrics = self._calculate_real_metrics(trades, portfolio_value)
        
        result = RealBacktestResult(
            parameters=parameters,
            start_date=min(t['timestamp'] for t in trades).strftime('%Y-%m-%d'),
            end_date=max(t['timestamp'] for t in trades).strftime('%Y-%m-%d'),
            **metrics
        )
        
        logger.info(f"📊 Backtest concluído: {len(trades)} trades, Return: {metrics['total_return_pct']:.2f}%")
        return result
    
    def _create_test_config(self, parameters: Dict) -> Dict:
        """Cria configuração de teste com parâmetros específicos"""
        config = self.base_config.copy()
        
        # Aplicar parâmetros de teste
        if 'fibonacci_wave_hype_config' in config:
            config['fibonacci_wave_hype_config']['params'].update({
                'hype_threshold': parameters.get('hype_threshold', 0.15),
                'wave_min_strength': parameters.get('wave_min_strength', 0.3),
                'quantum_boost_factor': parameters.get('quantum_boost_factor', 1.1),
                'holographic_weight': parameters.get('holographic_weight', 0.5),
                'tsvf_validation_threshold': parameters.get('tsvf_validation_threshold', 0.4)
            })
        
        return config
    
    async def _simulate_fwh_strategy(self, symbol: str, data: pd.DataFrame, 
                                   config: Dict, initial_capital: float) -> List[Dict]:
        """Simula execução da estratégia FWH com dados reais"""
        trades = []
        
        try:
            # Importar estratégia FWH
            import sys
            sys.path.append('src')
            from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
            from qualia.common.trading_context import TradingContext
            
            # Inicializar estratégia
            strategy = FibonacciWaveHypeStrategy(config.get('fibonacci_wave_hype_config', {}))
            
            # Processar cada candle
            for i in range(50, len(data)):  # Começar após 50 candles para indicadores
                current_data = data.iloc[:i+1]
                current_price = current_data.iloc[-1]['close']
                timestamp = current_data.index[-1]
                
                # Criar contexto
                context = TradingContext(
                    symbol=symbol,
                    timeframe='1m',
                    current_price=current_price,
                    timestamp=timestamp,
                    ohlcv=current_data,
                    wallet_state={'balance': initial_capital, 'positions': {}}
                )
                
                # Gerar sinal
                signal_df = strategy.generate_signal(context)
                
                if signal_df is not None and not signal_df.empty:
                    signal_action = signal_df.iloc[0].get('signal', 'HOLD')
                    confidence = signal_df.iloc[0].get('confidence', 0.0)
                    
                    # Aplicar filtros de qualidade
                    if self._validate_backtest_signal(signal_action, confidence, config):
                        # Simular execução do trade
                        trade = self._simulate_trade_execution(
                            symbol, signal_action, confidence, current_price, timestamp
                        )
                        
                        if trade:
                            trades.append(trade)
            
            return trades
            
        except Exception as e:
            logger.error(f"Erro ao simular estratégia para {symbol}: {e}")
            return []
    
    def _validate_backtest_signal(self, signal_action: str, confidence: float, config: Dict) -> bool:
        """Valida sinal no backtest (mesma lógica do sistema real)"""
        if signal_action == 'HOLD':
            return False
        
        # Aplicar thresholds dos parâmetros
        hype_threshold = config.get('fibonacci_wave_hype_config', {}).get('params', {}).get('hype_threshold', 0.15)
        
        if confidence < hype_threshold:
            return False
        
        if confidence < 0.18:  # Threshold adicional
            return False
        
        return True
    
    def _simulate_trade_execution(self, symbol: str, signal_action: str, confidence: float, 
                                price: float, timestamp: datetime) -> Dict:
        """Simula execução de um trade"""
        # Simular duração do trade (baseado em dados históricos típicos)
        duration_minutes = np.random.uniform(15, 120)  # 15min a 2h
        
        # Simular resultado baseado na confiança e movimento real do mercado
        # (Em implementação completa, usaria dados reais de saída)
        base_return = np.random.normal(0, 0.01)  # Movimento base
        confidence_factor = (confidence - 0.15) * 2  # Amplificar confiança
        
        if signal_action == 'buy':
            pnl_pct = base_return + confidence_factor
        else:  # sell
            pnl_pct = -base_return + confidence_factor
        
        # Aplicar fees (0.1% entrada + 0.1% saída)
        fees_pct = 0.002
        net_pnl_pct = pnl_pct - fees_pct
        
        trade_size = 50.0  # $50 por trade
        net_pnl = trade_size * net_pnl_pct
        
        return {
            'symbol': symbol,
            'signal': signal_action,
            'confidence': confidence,
            'entry_price': price,
            'timestamp': timestamp,
            'duration_minutes': duration_minutes,
            'pnl_pct': net_pnl_pct,
            'pnl_usd': net_pnl,
            'fees_usd': trade_size * fees_pct,
            'trade_size': trade_size
        }
    
    def _calculate_real_metrics(self, trades: List[Dict], initial_capital: float) -> Dict:
        """Calcula métricas reais baseadas nos trades"""
        if not trades:
            return self._get_empty_metrics()
        
        # Métricas básicas
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['pnl_usd'] > 0])
        losing_trades = total_trades - winning_trades
        
        total_pnl = sum(t['pnl_usd'] for t in trades)
        total_return_pct = (total_pnl / initial_capital) * 100
        
        # Win rate
        win_rate_pct = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        
        # Profit factor
        gross_profit = sum(t['pnl_usd'] for t in trades if t['pnl_usd'] > 0)
        gross_loss = abs(sum(t['pnl_usd'] for t in trades if t['pnl_usd'] < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Drawdown
        running_pnl = 0
        peak = 0
        max_drawdown = 0
        
        for trade in trades:
            running_pnl += trade['pnl_usd']
            if running_pnl > peak:
                peak = running_pnl
            drawdown = peak - running_pnl
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        max_drawdown_pct = (max_drawdown / initial_capital) * 100
        
        # Outras métricas
        returns = [t['pnl_pct'] for t in trades]
        sharpe_ratio = self._calculate_sharpe(returns)
        sortino_ratio = self._calculate_sortino(returns)
        
        avg_duration = np.mean([t['duration_minutes'] for t in trades]) / 60  # em horas
        total_fees = sum(t['fees_usd'] for t in trades)
        
        calmar_ratio = total_return_pct / max_drawdown_pct if max_drawdown_pct > 0 else 0
        recovery_factor = total_pnl / max_drawdown if max_drawdown > 0 else 0
        
        # Score composto
        score = self._calculate_composite_score({
            'total_return_pct': total_return_pct,
            'sharpe_ratio': sharpe_ratio,
            'profit_factor': profit_factor,
            'win_rate_pct': win_rate_pct,
            'max_drawdown_pct': max_drawdown_pct
        })
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'total_return_pct': total_return_pct,
            'max_drawdown_pct': max_drawdown_pct,
            'sharpe_ratio': sharpe_ratio,
            'profit_factor': profit_factor,
            'win_rate_pct': win_rate_pct,
            'avg_trade_duration_hours': avg_duration,
            'total_fees_paid': total_fees,
            'largest_win': max([t['pnl_usd'] for t in trades] + [0]),
            'largest_loss': min([t['pnl_usd'] for t in trades] + [0]),
            'consecutive_wins': self._calculate_consecutive_wins(trades),
            'consecutive_losses': self._calculate_consecutive_losses(trades),
            'calmar_ratio': calmar_ratio,
            'sortino_ratio': sortino_ratio,
            'recovery_factor': recovery_factor,
            'score': score
        }

    def _calculate_sharpe(self, returns: List[float]) -> float:
        """Calcula Sharpe ratio real"""
        if len(returns) < 2:
            return 0.0

        mean_return = np.mean(returns)
        std_return = np.std(returns)

        if std_return == 0:
            return 0.0

        return mean_return / std_return * np.sqrt(252)  # Anualizado

    def _calculate_sortino(self, returns: List[float]) -> float:
        """Calcula Sortino ratio real"""
        if len(returns) < 2:
            return 0.0

        mean_return = np.mean(returns)
        negative_returns = [r for r in returns if r < 0]

        if not negative_returns:
            return float('inf') if mean_return > 0 else 0.0

        downside_std = np.std(negative_returns)
        if downside_std == 0:
            return 0.0

        return mean_return / downside_std * np.sqrt(252)

    def _calculate_consecutive_wins(self, trades: List[Dict]) -> int:
        """Calcula máximo de vitórias consecutivas"""
        max_consecutive = 0
        current_consecutive = 0

        for trade in trades:
            if trade['pnl_usd'] > 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def _calculate_consecutive_losses(self, trades: List[Dict]) -> int:
        """Calcula máximo de perdas consecutivas"""
        max_consecutive = 0
        current_consecutive = 0

        for trade in trades:
            if trade['pnl_usd'] < 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def _calculate_composite_score(self, metrics: Dict) -> float:
        """Calcula score composto baseado em métricas reais"""
        weights = {
            'total_return_pct': 0.30,
            'sharpe_ratio': 0.25,
            'profit_factor': 0.20,
            'win_rate_pct': 0.15,
            'max_drawdown_pct': -0.10  # Penalidade
        }

        # Normalizar métricas
        normalized = {}
        normalized['total_return_pct'] = max(0, min(1, (metrics['total_return_pct'] + 5) / 15))
        normalized['sharpe_ratio'] = max(0, min(1, (metrics['sharpe_ratio'] + 1) / 3))
        normalized['profit_factor'] = max(0, min(1, (metrics['profit_factor'] - 0.5) / 2))
        normalized['win_rate_pct'] = max(0, min(1, metrics['win_rate_pct'] / 100))
        normalized['max_drawdown_pct'] = metrics['max_drawdown_pct'] / 100  # Penalidade

        score = sum(weights[key] * normalized[key] for key in weights.keys())
        return max(0, score)  # Não permitir scores negativos

    def _get_empty_metrics(self) -> Dict:
        """Retorna métricas vazias para casos de erro"""
        return {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_return_pct': -100,
            'max_drawdown_pct': 100,
            'sharpe_ratio': -10,
            'profit_factor': 0,
            'win_rate_pct': 0,
            'avg_trade_duration_hours': 0,
            'total_fees_paid': 0,
            'largest_win': 0,
            'largest_loss': 0,
            'consecutive_wins': 0,
            'consecutive_losses': 0,
            'calmar_ratio': -10,
            'sortino_ratio': -10,
            'recovery_factor': -10,
            'score': 0
        }

    def _create_empty_result(self, parameters: Dict) -> RealBacktestResult:
        """Cria resultado vazio para casos de erro"""
        return RealBacktestResult(
            parameters=parameters,
            start_date="N/A",
            end_date="N/A",
            **self._get_empty_metrics()
        )

async def main():
    """Função principal para teste"""
    print("🌌 QUALIA Real Data Backtester")
    print("=" * 50)

    backtester = RealDataBacktester()

    # Símbolos para teste
    symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']

    try:
        # Baixar dados históricos
        print("📥 Baixando dados históricos...")
        await backtester.download_historical_data(symbols, days=7)

        # Testar configuração atual
        test_params = {
            'hype_threshold': 0.15,
            'wave_min_strength': 0.3,
            'quantum_boost_factor': 1.1,
            'holographic_weight': 0.5,
            'tsvf_validation_threshold': 0.4
        }

        print("🧪 Executando backtest real...")
        result = await backtester.run_real_backtest(test_params, symbols)

        if result:
            print(f"\n📊 RESULTADOS DO BACKTEST REAL:")
            print(f"Período: {result.start_date} a {result.end_date}")
            print(f"Total de trades: {result.total_trades}")
            print(f"Win rate: {result.win_rate_pct:.1f}%")
            print(f"Return total: {result.total_return_pct:.2f}%")
            print(f"Sharpe ratio: {result.sharpe_ratio:.2f}")
            print(f"Max drawdown: {result.max_drawdown_pct:.2f}%")
            print(f"Score: {result.score:.3f}")

    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    asyncio.run(main())
