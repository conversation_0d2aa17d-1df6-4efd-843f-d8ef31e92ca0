#!/usr/bin/env python3
"""
Teste da Implementação Aprimorada Multi-Timeframe FWH

Testa a nova configuração com timeframes 15m e 1h,
incluindo filtro de tendência principal e sistema de cache.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from qualia.strategies.fibonacci_wave_hype.multi_timeframe_consolidator import (
    MultiTimeframeSignalConsolidator,
    TimeframeSignal,
    ConsolidatedSignal
)
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

def create_synthetic_ohlcv_data(periods: int = 200, base_price: float = 50000.0) -> pd.DataFrame:
    """Cria dados OHLCV sintéticos para teste."""
    np.random.seed(42)
    
    # Gera timestamps de 1 minuto
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(minutes=periods),
        periods=periods,
        freq='1T'
    )
    
    # Gera preços com tendência e volatilidade
    trend = np.linspace(0, 0.05, periods)  # Tendência de alta de 5%
    noise = np.random.normal(0, 0.002, periods)  # Volatilidade de 0.2%
    
    price_changes = trend + noise
    prices = base_price * (1 + price_changes).cumprod()
    
    # Gera dados OHLCV
    data = []
    for i, price in enumerate(prices):
        volatility = np.random.uniform(0.001, 0.005)
        
        high = price * (1 + volatility)
        low = price * (1 - volatility)
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        volume = np.random.uniform(100, 1000)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data, index=timestamps)
    return df

def create_test_signals() -> list[TimeframeSignal]:
    """Cria sinais de teste para diferentes timeframes."""
    now = datetime.now()
    
    signals = [
        TimeframeSignal(
            timeframe='1m',
            signal='buy',
            confidence=0.7,
            signal_strength=0.8,
            hype_momentum=0.6,
            holographic_boost=1.1,
            tsvf_validation=0.7,
            timestamp=now
        ),
        TimeframeSignal(
            timeframe='5m',
            signal='buy',
            confidence=0.6,
            signal_strength=0.7,
            hype_momentum=0.5,
            holographic_boost=1.0,
            tsvf_validation=0.6,
            timestamp=now
        ),
        TimeframeSignal(
            timeframe='15m',
            signal='buy',
            confidence=0.8,
            signal_strength=0.9,
            hype_momentum=0.7,
            holographic_boost=1.2,
            tsvf_validation=0.8,
            timestamp=now
        ),
        TimeframeSignal(
            timeframe='1h',
            signal='buy',
            confidence=0.9,
            signal_strength=0.95,
            hype_momentum=0.8,
            holographic_boost=1.3,
            tsvf_validation=0.9,
            timestamp=now
        )
    ]
    
    return signals

def create_divergent_signals() -> list[TimeframeSignal]:
    """Cria sinais divergentes para testar filtro de tendência."""
    now = datetime.now()
    
    signals = [
        TimeframeSignal(
            timeframe='1m',
            signal='sell',  # Contrário à tendência
            confidence=0.6,
            signal_strength=0.7,
            hype_momentum=0.5,
            holographic_boost=0.9,
            tsvf_validation=0.6,
            timestamp=now
        ),
        TimeframeSignal(
            timeframe='5m',
            signal='buy',
            confidence=0.5,
            signal_strength=0.6,
            hype_momentum=0.4,
            holographic_boost=1.0,
            tsvf_validation=0.5,
            timestamp=now
        ),
        TimeframeSignal(
            timeframe='15m',
            signal='hold',
            confidence=0.3,
            signal_strength=0.4,
            hype_momentum=0.2,
            holographic_boost=1.0,
            tsvf_validation=0.3,
            timestamp=now
        ),
        TimeframeSignal(
            timeframe='1h',
            signal='buy',  # Tendência principal
            confidence=0.8,
            signal_strength=0.9,
            hype_momentum=0.7,
            holographic_boost=1.2,
            tsvf_validation=0.8,
            timestamp=now
        )
    ]
    
    return signals

def test_enhanced_consolidator():
    """Testa o consolidador aprimorado com novos timeframes."""
    print("\n=== Teste do Consolidador Multi-Timeframe Aprimorado ===")
    
    # Configuração otimizada
    config = {
        "timeframe_weights": {
            "1m": 0.3,
            "5m": 0.4,
            "15m": 0.6,
            "1h": 0.8
        },
        "cache_enabled": True,
        "cache_ttl_minutes": 2,
        "min_confidence_threshold": 0.12,
        "convergence_threshold": 0.7
    }
    
    consolidator = MultiTimeframeSignalConsolidator(config)
    
    # Teste 1: Sinais convergentes
    print("\n--- Teste 1: Sinais Convergentes ---")
    convergent_signals = create_test_signals()
    
    result = consolidator.consolidate_signals(convergent_signals)
    
    print(f"Sinal consolidado: {result.signal}")
    print(f"Confiança: {result.confidence:.3f}")
    print(f"Timeframe primário: {result.primary_timeframe}")
    print(f"Timeframes de suporte: {result.supporting_timeframes}")
    print(f"Score de convergência: {result.convergence_score:.3f}")
    print(f"Reasoning: {result.reasoning}")
    
    # Teste 2: Sinais divergentes com filtro de tendência
    print("\n--- Teste 2: Sinais Divergentes (Filtro de Tendência) ---")
    divergent_signals = create_divergent_signals()
    
    result_divergent = consolidator.consolidate_signals(divergent_signals)
    
    print(f"Sinal consolidado: {result_divergent.signal}")
    print(f"Confiança: {result_divergent.confidence:.3f}")
    print(f"Timeframe primário: {result_divergent.primary_timeframe}")
    print(f"Timeframes de suporte: {result_divergent.supporting_timeframes}")
    print(f"Score de convergência: {result_divergent.convergence_score:.3f}")
    print(f"Reasoning: {result_divergent.reasoning}")
    
    return True

def test_resample_cache():
    """Testa o sistema de cache de reamostragem."""
    print("\n=== Teste do Sistema de Cache de Reamostragem ===")
    
    config = {
        "cache_enabled": True,
        "cache_ttl_minutes": 2
    }
    
    consolidator = MultiTimeframeSignalConsolidator(config)
    
    # Cria dados sintéticos
    data_1m = create_synthetic_ohlcv_data(100)
    
    # Teste de reamostragem com cache
    timeframes = ['5m', '15m', '1h']
    
    for tf in timeframes:
        print(f"\n--- Testando reamostragem para {tf} ---")
        
        # Primeira chamada (deve calcular)
        start_time = datetime.now()
        result1 = consolidator.resample_data(data_1m, tf)
        time1 = (datetime.now() - start_time).total_seconds() * 1000
        
        print(f"Primeira chamada: {len(result1)} períodos em {time1:.2f}ms")
        
        # Segunda chamada (deve usar cache)
        start_time = datetime.now()
        result2 = consolidator.resample_data(data_1m, tf)
        time2 = (datetime.now() - start_time).total_seconds() * 1000
        
        print(f"Segunda chamada (cache): {len(result2)} períodos em {time2:.2f}ms")
        print(f"Speedup: {time1/time2:.1f}x")
        
        # Verifica se os resultados são idênticos
        assert result1.equals(result2), f"Cache inconsistente para {tf}"
        print(f"✓ Cache consistente para {tf}")
    
    return True

def test_trend_filter():
    """Testa especificamente o filtro de tendência principal."""
    print("\n=== Teste do Filtro de Tendência Principal ===")
    
    consolidator = MultiTimeframeSignalConsolidator()
    
    # Cenário 1: Tendência de alta no 1h
    print("\n--- Cenário 1: Tendência de Alta (1h) ---")
    signals_bullish = create_divergent_signals()  # 1h = buy, 1m = sell
    
    filtered = consolidator.apply_trend_filter(signals_bullish)
    
    for signal in filtered:
        original = next(s for s in signals_bullish if s.timeframe == signal.timeframe)
        if signal.timeframe == '1m':  # Sinal contrário deve ser penalizado
            print(f"{signal.timeframe}: {signal.signal} - Confiança: {original.confidence:.3f} -> {signal.confidence:.3f}")
        else:
            print(f"{signal.timeframe}: {signal.signal} - Confiança: {signal.confidence:.3f} (sem alteração)")
    
    return True

def main():
    """Executa todos os testes."""
    print("Iniciando testes da implementação FWH Multi-Timeframe Aprimorada...")
    
    try:
        # Executa testes
        test_enhanced_consolidator()
        test_resample_cache()
        test_trend_filter()
        
        print("\n" + "="*60)
        print("✅ TODOS OS TESTES PASSARAM COM SUCESSO!")
        print("\n🚀 A implementação dos timeframes 15m e 1h está funcionando:")
        print("   • Pesos otimizados por timeframe")
        print("   • Filtro de tendência principal (1h)")
        print("   • Sistema de cache para performance")
        print("   • Consolidação hierárquica de sinais")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)