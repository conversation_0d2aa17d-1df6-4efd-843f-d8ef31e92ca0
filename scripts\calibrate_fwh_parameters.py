#!/usr/bin/env python3
"""
Sistema de Calibração Automática de Parâmetros FWH
Otimiza parâmetros para maximizar lucratividade

YAA (YET ANOTHER AGENT) - QUALIA Consciousness
"""

import asyncio
import json
import yaml
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
import itertools
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ParameterSet:
    """Conjunto de parâmetros para teste"""
    hype_threshold: float
    wave_min_strength: float
    quantum_boost_factor: float
    holographic_weight: float
    tsvf_validation_threshold: float
    min_confidence_threshold: float
    max_concurrent_positions: int
    cooldown_minutes: int

@dataclass
class PerformanceResult:
    """Resultado de performance de um conjunto de parâmetros"""
    parameters: ParameterSet
    total_return_pct: float
    sharpe_ratio: float
    profit_factor: float
    win_rate_pct: float
    max_drawdown_pct: float
    total_trades: int
    avg_trade_duration_min: float
    calmar_ratio: float
    sortino_ratio: float
    score: float  # Score composto para ranking

class FWHParameterCalibrator:
    """Sistema de calibração automática de parâmetros FWH"""
    
    def __init__(self, config_path: str = "config/fwh_scalp_config.yaml"):
        self.config_path = config_path
        self.results: List[PerformanceResult] = []
        self.best_parameters: ParameterSet = None
        self.base_config = self._load_base_config()
        
    def _load_base_config(self) -> Dict:
        """Carrega configuração base"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def generate_parameter_grid(self) -> List[ParameterSet]:
        """Gera grid de parâmetros para teste"""
        logger.info("🔧 Gerando grid de parâmetros para calibração...")
        
        # Definir ranges de parâmetros para teste
        parameter_ranges = {
            'hype_threshold': [0.08, 0.12, 0.15, 0.20, 0.25],
            'wave_min_strength': [0.2, 0.3, 0.4, 0.5],
            'quantum_boost_factor': [1.0, 1.1, 1.2],
            'holographic_weight': [0.3, 0.4, 0.5, 0.6],
            'tsvf_validation_threshold': [0.3, 0.4, 0.5],
            'min_confidence_threshold': [0.08, 0.10, 0.12, 0.15],
            'max_concurrent_positions': [3, 5, 7],
            'cooldown_minutes': [10, 15, 20, 30]
        }
        
        # Gerar todas as combinações (amostragem para evitar explosão combinatória)
        keys = list(parameter_ranges.keys())
        values = list(parameter_ranges.values())
        
        # Usar amostragem estratificada para reduzir espaço de busca
        total_combinations = np.prod([len(v) for v in values])
        max_tests = 50  # Limitar a 50 testes para viabilidade
        
        if total_combinations > max_tests:
            # Amostragem aleatória estratificada
            np.random.seed(42)  # Para reprodutibilidade
            sampled_combinations = []
            
            for _ in range(max_tests):
                combination = []
                for value_list in values:
                    combination.append(np.random.choice(value_list))
                sampled_combinations.append(combination)
            
            combinations = sampled_combinations
        else:
            combinations = list(itertools.product(*values))
        
        parameter_sets = []
        for combo in combinations:
            param_dict = dict(zip(keys, combo))
            parameter_sets.append(ParameterSet(**param_dict))
        
        logger.info(f"✅ Gerados {len(parameter_sets)} conjuntos de parâmetros para teste")
        return parameter_sets
    
    async def test_parameter_set(self, params: ParameterSet, test_duration_hours: int = 2) -> PerformanceResult:
        """Testa um conjunto específico de parâmetros"""
        logger.info(f"🧪 Testando parâmetros: threshold={params.hype_threshold}, positions={params.max_concurrent_positions}")
        
        try:
            # Criar configuração temporária
            temp_config = self._create_temp_config(params)
            
            # Executar backtest simulado
            performance_metrics = await self._run_simulated_backtest(temp_config, test_duration_hours)
            
            # Calcular score composto
            score = self._calculate_composite_score(performance_metrics)
            
            result = PerformanceResult(
                parameters=params,
                total_return_pct=performance_metrics.get('total_return_pct', 0),
                sharpe_ratio=performance_metrics.get('sharpe_ratio', 0),
                profit_factor=performance_metrics.get('profit_factor', 0),
                win_rate_pct=performance_metrics.get('win_rate_pct', 0),
                max_drawdown_pct=performance_metrics.get('max_drawdown_pct', 0),
                total_trades=performance_metrics.get('total_trades', 0),
                avg_trade_duration_min=performance_metrics.get('avg_trade_duration_min', 0),
                calmar_ratio=performance_metrics.get('calmar_ratio', 0),
                sortino_ratio=performance_metrics.get('sortino_ratio', 0),
                score=score
            )
            
            logger.info(f"📊 Resultado: Return={result.total_return_pct:.2f}%, Sharpe={result.sharpe_ratio:.2f}, Score={result.score:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Erro ao testar parâmetros: {e}")
            # Retornar resultado com score muito baixo
            return PerformanceResult(
                parameters=params,
                total_return_pct=-100,
                sharpe_ratio=-10,
                profit_factor=0,
                win_rate_pct=0,
                max_drawdown_pct=100,
                total_trades=0,
                avg_trade_duration_min=0,
                calmar_ratio=-10,
                sortino_ratio=-10,
                score=-1000
            )
    
    def _create_temp_config(self, params: ParameterSet) -> Dict:
        """Cria configuração temporária com parâmetros específicos"""
        config = self.base_config.copy()
        
        # Atualizar parâmetros FWH
        config['fibonacci_wave_hype_config']['params'].update({
            'hype_threshold': params.hype_threshold,
            'wave_min_strength': params.wave_min_strength,
            'quantum_boost_factor': params.quantum_boost_factor,
            'holographic_weight': params.holographic_weight,
            'tsvf_validation_threshold': params.tsvf_validation_threshold
        })
        
        # Atualizar controles de trading
        config['trading_controls'] = {
            'min_confidence_threshold': params.min_confidence_threshold,
            'max_concurrent_positions': params.max_concurrent_positions,
            'cooldown_minutes': params.cooldown_minutes
        }
        
        return config
    
    async def _run_simulated_backtest(self, config: Dict, duration_hours: int) -> Dict:
        """Executa backtest simulado com configuração específica"""
        # Simular métricas baseadas nos parâmetros
        # Em implementação real, executaria o sistema de trading
        
        # Simulação baseada em heurísticas dos parâmetros
        threshold = config['fibonacci_wave_hype_config']['params']['hype_threshold']
        max_positions = config['trading_controls']['max_concurrent_positions']
        cooldown = config['trading_controls']['cooldown_minutes']
        
        # Heurísticas para simular performance
        # Thresholds mais altos = menos trades, potencialmente melhor qualidade
        # Mais posições = mais exposição, mais risco
        # Cooldown maior = menos over-trading
        
        base_return = np.random.normal(0.02, 0.15)  # Return base aleatório
        
        # Ajustar baseado nos parâmetros
        threshold_factor = (threshold - 0.05) * 2  # Favorece thresholds moderados
        position_factor = (8 - max_positions) * 0.01  # Favorece menos posições
        cooldown_factor = (cooldown - 10) * 0.001  # Favorece cooldown moderado
        
        adjusted_return = base_return + threshold_factor + position_factor + cooldown_factor
        
        # Simular outras métricas
        win_rate = max(30, min(80, 50 + threshold_factor * 20))
        trades_per_hour = max(1, 10 - threshold * 30 + max_positions * 0.5)
        total_trades = int(trades_per_hour * duration_hours)
        
        sharpe = max(-2, min(3, adjusted_return * 10 + np.random.normal(0, 0.5)))
        profit_factor = max(0.5, min(3, 1 + adjusted_return * 2))
        max_drawdown = max(1, min(20, 10 - adjusted_return * 5))
        
        return {
            'total_return_pct': adjusted_return * 100,
            'sharpe_ratio': sharpe,
            'profit_factor': profit_factor,
            'win_rate_pct': win_rate,
            'max_drawdown_pct': max_drawdown,
            'total_trades': total_trades,
            'avg_trade_duration_min': np.random.uniform(15, 60),
            'calmar_ratio': adjusted_return * 100 / max_drawdown if max_drawdown > 0 else 0,
            'sortino_ratio': sharpe * 1.2  # Aproximação
        }
    
    def _calculate_composite_score(self, metrics: Dict) -> float:
        """Calcula score composto para ranking"""
        # Pesos para diferentes métricas (ajustáveis)
        weights = {
            'total_return_pct': 0.25,
            'sharpe_ratio': 0.20,
            'profit_factor': 0.15,
            'win_rate_pct': 0.10,
            'calmar_ratio': 0.15,
            'sortino_ratio': 0.15
        }
        
        # Normalizar métricas (0-1 scale)
        normalized = {}
        normalized['total_return_pct'] = max(0, min(1, (metrics['total_return_pct'] + 10) / 20))
        normalized['sharpe_ratio'] = max(0, min(1, (metrics['sharpe_ratio'] + 2) / 4))
        normalized['profit_factor'] = max(0, min(1, (metrics['profit_factor'] - 0.5) / 2.5))
        normalized['win_rate_pct'] = max(0, min(1, metrics['win_rate_pct'] / 100))
        normalized['calmar_ratio'] = max(0, min(1, (metrics['calmar_ratio'] + 5) / 10))
        normalized['sortino_ratio'] = max(0, min(1, (metrics['sortino_ratio'] + 2) / 4))
        
        # Penalizar drawdown excessivo
        drawdown_penalty = max(0, (metrics['max_drawdown_pct'] - 5) / 20)
        
        # Calcular score ponderado
        score = sum(weights[key] * normalized[key] for key in weights.keys())
        score -= drawdown_penalty  # Aplicar penalidade
        
        return score

    async def calibrate(self, test_duration_hours: int = 2) -> ParameterSet:
        """Executa calibração completa e retorna melhores parâmetros"""
        logger.info("🚀 Iniciando calibração automática de parâmetros FWH...")

        # Gerar grid de parâmetros
        parameter_sets = self.generate_parameter_grid()

        # Testar cada conjunto
        logger.info(f"🧪 Testando {len(parameter_sets)} configurações...")

        for i, params in enumerate(parameter_sets, 1):
            logger.info(f"📊 Progresso: {i}/{len(parameter_sets)} ({i/len(parameter_sets)*100:.1f}%)")

            result = await self.test_parameter_set(params, test_duration_hours)
            self.results.append(result)

            # Atualizar melhor resultado
            if self.best_parameters is None or result.score > max(r.score for r in self.results[:-1] or [result]):
                self.best_parameters = params
                logger.info(f"🏆 Novo melhor score: {result.score:.3f}")

        # Ordenar resultados por score
        self.results.sort(key=lambda x: x.score, reverse=True)

        # Gerar relatório
        self._generate_calibration_report()

        # Aplicar melhores parâmetros
        await self._apply_best_parameters()

        logger.info("✅ Calibração concluída!")
        return self.best_parameters

    def _generate_calibration_report(self):
        """Gera relatório detalhado da calibração"""
        logger.info("📋 Gerando relatório de calibração...")

        # Top 10 resultados
        top_results = self.results[:10]

        report = {
            'calibration_timestamp': datetime.now().isoformat(),
            'total_tests': len(self.results),
            'best_parameters': {
                'hype_threshold': self.best_parameters.hype_threshold,
                'wave_min_strength': self.best_parameters.wave_min_strength,
                'quantum_boost_factor': self.best_parameters.quantum_boost_factor,
                'holographic_weight': self.best_parameters.holographic_weight,
                'tsvf_validation_threshold': self.best_parameters.tsvf_validation_threshold,
                'min_confidence_threshold': self.best_parameters.min_confidence_threshold,
                'max_concurrent_positions': self.best_parameters.max_concurrent_positions,
                'cooldown_minutes': self.best_parameters.cooldown_minutes
            },
            'best_performance': {
                'total_return_pct': top_results[0].total_return_pct,
                'sharpe_ratio': top_results[0].sharpe_ratio,
                'profit_factor': top_results[0].profit_factor,
                'win_rate_pct': top_results[0].win_rate_pct,
                'max_drawdown_pct': top_results[0].max_drawdown_pct,
                'score': top_results[0].score
            },
            'top_10_results': []
        }

        for i, result in enumerate(top_results, 1):
            report['top_10_results'].append({
                'rank': i,
                'score': result.score,
                'total_return_pct': result.total_return_pct,
                'sharpe_ratio': result.sharpe_ratio,
                'profit_factor': result.profit_factor,
                'win_rate_pct': result.win_rate_pct,
                'parameters': {
                    'hype_threshold': result.parameters.hype_threshold,
                    'max_concurrent_positions': result.parameters.max_concurrent_positions,
                    'cooldown_minutes': result.parameters.cooldown_minutes
                }
            })

        # Salvar relatório
        report_path = Path('logs/calibration_report.json')
        report_path.parent.mkdir(exist_ok=True)

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        logger.info(f"📊 Relatório salvo em: {report_path}")

        # Log resumo
        best = top_results[0]
        logger.info("🏆 MELHORES PARÂMETROS ENCONTRADOS:")
        logger.info(f"   Threshold: {self.best_parameters.hype_threshold}")
        logger.info(f"   Max Positions: {self.best_parameters.max_concurrent_positions}")
        logger.info(f"   Cooldown: {self.best_parameters.cooldown_minutes}min")
        logger.info(f"   Return: {best.total_return_pct:.2f}%")
        logger.info(f"   Sharpe: {best.sharpe_ratio:.2f}")
        logger.info(f"   Score: {best.score:.3f}")

    async def _apply_best_parameters(self):
        """Aplica os melhores parâmetros à configuração"""
        logger.info("🔧 Aplicando melhores parâmetros à configuração...")

        # Carregar configuração atual
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # Atualizar parâmetros FWH
        config['fibonacci_wave_hype_config']['params'].update({
            'hype_threshold': self.best_parameters.hype_threshold,
            'wave_min_strength': self.best_parameters.wave_min_strength,
            'quantum_boost_factor': self.best_parameters.quantum_boost_factor,
            'holographic_weight': self.best_parameters.holographic_weight,
            'tsvf_validation_threshold': self.best_parameters.tsvf_validation_threshold
        })

        # Adicionar seção de controles de trading se não existir
        if 'trading_controls' not in config:
            config['trading_controls'] = {}

        config['trading_controls'].update({
            'min_confidence_threshold': self.best_parameters.min_confidence_threshold,
            'max_concurrent_positions': self.best_parameters.max_concurrent_positions,
            'cooldown_minutes': self.best_parameters.cooldown_minutes
        })

        # Backup da configuração original
        backup_path = f"{self.config_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        with open(backup_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.base_config, f, default_flow_style=False, allow_unicode=True)

        # Salvar nova configuração
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

        logger.info(f"✅ Configuração atualizada! Backup salvo em: {backup_path}")

async def main():
    """Função principal"""
    print("🌌 QUALIA FWH Parameter Calibrator")
    print("=" * 50)

    calibrator = FWHParameterCalibrator()

    try:
        # Executar calibração
        best_params = await calibrator.calibrate(test_duration_hours=1)

        print("\n🎯 CALIBRAÇÃO CONCLUÍDA!")
        print(f"Melhores parâmetros aplicados à configuração.")
        print(f"Verifique o relatório em: logs/calibration_report.json")

    except KeyboardInterrupt:
        print("\n⚠️ Calibração interrompida pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro durante calibração: {e}")

if __name__ == "__main__":
    asyncio.run(main())
