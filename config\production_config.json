{"system": {"name": "QUALIA Optimized Production System", "version": "1.0.0", "mode": "paper_trading", "capital": 10000.0, "symbols": ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "SOLUSDT", "DOTUSDT", "LINKUSDT", "POLUSDT"], "timeframes": ["5m", "15m", "1h"], "risk_profile": "balanced", "data_source": "kraken", "base_currency": "USDT", "quote_currencies": ["USDT", "USD", "EUR"]}, "study_name": "qualia_production_optimizer", "n_trials_per_cycle": 25, "optimization_cycles": 500, "symbols": ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "SOLUSDT", "DOTUSDT", "LINKUSDT", "POLUSDT"], "base_params": {"price_amplification": 1.0, "news_amplification": 10.0, "min_confidence": 0.37}, "optimization_discoveries": {"news_amplification_impact": "+463.2% (fator mais impactante)", "price_amplification_impact": "-51.4% (deve ser reduzido)", "min_confidence_impact": "-7.0% (próximo ao original)", "convergence": "rápida com melhoria consistente"}, "worker_settings": {"cycle_interval_minutes": 15, "max_runtime_hours": 24, "max_concurrent_trials": 4, "timeout_per_trial_seconds": 300, "restart_on_failure": true, "max_consecutive_failures": 5, "performance_threshold_pnl": 0.05, "save_interval_cycles": 10, "log_level": "INFO", "enable_detailed_logging": false, "memory_limit_mb": 2048, "cpu_limit_percent": 80}, "optimization_settings": {"sampler": "TPESampler", "pruner": "Median<PERSON><PERSON><PERSON>", "n_startup_trials": 5, "n_warmup_steps": 3, "interval_steps": 1, "n_trials_per_cycle": 25, "direction": "maximize", "load_if_exists": true, "storage_url": "sqlite:///results/bayesian_optimization/production_study.db", "comment": "Reduzido startup_trials e warmup para convergência rápida descoberta"}, "parameter_ranges": {"price_amplification": {"min": 0.5, "max": 2.0, "step": 0.05, "comment": "Foco ao redor do ótimo 1.0 (-51.4% descoberto)"}, "news_amplification": {"min": 8.0, "max": 15.0, "step": 0.2, "comment": "Foco ao redor do ótimo 11.3 (+463.2% descoberto)"}, "min_confidence": {"min": 0.3, "max": 0.45, "step": 0.01, "comment": "Foco ao redor do ótimo 0.37 (-7.0% descoberto)"}}, "symbol_specific_settings": {"BTCUSDT": {"weight": 0.3, "min_confidence_override": 0.3, "volatility_adjustment": 1.0}, "ETHUSDT": {"weight": 0.25, "min_confidence_override": 0.4, "volatility_adjustment": 1.1}, "BNBUSDT": {"weight": 0.15, "min_confidence_override": 0.5, "volatility_adjustment": 1.2}, "ADAUSDT": {"weight": 0.1, "min_confidence_override": 0.6, "volatility_adjustment": 1.3}, "SOLUSDT": {"weight": 0.1, "min_confidence_override": 0.5, "volatility_adjustment": 1.4}, "DOTUSDT": {"weight": 0.05, "min_confidence_override": 0.6, "volatility_adjustment": 1.2}, "LINKUSDT": {"weight": 0.03, "min_confidence_override": 0.7, "volatility_adjustment": 1.3}, "POLUSDT": {"weight": 0.02, "min_confidence_override": 0.7, "volatility_adjustment": 1.5}}, "risk_management": {"max_drawdown_percent": 5.0, "stop_loss_threshold": -2.0, "profit_target_threshold": 3.0, "position_size_limits": {"min_position": 0.01, "max_position": 0.5, "max_total_exposure": 1.0}}, "monitoring": {"enable_metrics": true, "metrics_interval_seconds": 60, "alert_thresholds": {"negative_pnl_consecutive_cycles": 5, "memory_usage_percent": 90, "cpu_usage_percent": 95, "error_rate_percent": 10}, "notification_settings": {"enable_email": false, "enable_webhook": false, "webhook_url": "", "email_recipients": []}}, "data_settings": {"cache_duration_hours": 1, "max_cache_size_mb": 500, "data_refresh_interval_minutes": 5, "fallback_to_cached_on_error": true, "max_retries": 3, "retry_delay_seconds": 5}}