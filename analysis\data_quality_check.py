#!/usr/bin/env python3
"""
Análise de Qualidade dos Dados Históricos QUALIA
YAA IMPLEMENTATION: Validação e caracterização dos dados para backtesting
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys
import warnings
warnings.filterwarnings('ignore')

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))
sys.path.append(str(Path(__file__).parent.parent / "src"))

def analyze_data_quality(file_path: str, symbol: str) -> dict:
    """Analisa qualidade e características dos dados históricos."""

    print(f"\n🔍 Analisando {symbol}...")

    # Carrega dados
    df = pd.read_csv(file_path)

    # Converte timestamp
    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
    df.set_index('datetime', inplace=True)

    # Análise básica
    analysis = {
        'symbol': symbol,
        'total_records': len(df),
        'date_range': {
            'start': df.index.min().strftime('%Y-%m-%d %H:%M'),
            'end': df.index.max().strftime('%Y-%m-%d %H:%M'),
            'duration_days': (df.index.max() - df.index.min()).days
        },
        'data_quality': {
            'missing_values': df.isnull().sum().to_dict(),
            'duplicate_timestamps': df.index.duplicated().sum(),
            'zero_volume_candles': (df['volume'] == 0).sum(),
            'negative_prices': (df[['open', 'high', 'low', 'close']] <= 0).sum().sum()
        },
        'price_statistics': {
            'close_min': df['close'].min(),
            'close_max': df['close'].max(),
            'close_mean': df['close'].mean(),
            'close_std': df['close'].std(),
            'daily_volatility': df['close'].pct_change().std() * np.sqrt(24),
            'annualized_volatility': df['close'].pct_change().std() * np.sqrt(24 * 365)
        },
        'volume_statistics': {
            'volume_min': df['volume'].min(),
            'volume_max': df['volume'].max(),
            'volume_mean': df['volume'].mean(),
            'volume_std': df['volume'].std()
        }
    }

    # Verifica consistência OHLC
    ohlc_issues = 0
    ohlc_issues += (df['high'] < df['low']).sum()
    ohlc_issues += (df['high'] < df['open']).sum()
    ohlc_issues += (df['high'] < df['close']).sum()
    ohlc_issues += (df['low'] > df['open']).sum()
    ohlc_issues += (df['low'] > df['close']).sum()

    analysis['data_quality']['ohlc_inconsistencies'] = ohlc_issues

    # Gaps temporais
    expected_interval = timedelta(hours=1)
    time_diffs = df.index.to_series().diff()[1:]
    gaps = (time_diffs != expected_interval).sum()
    analysis['data_quality']['temporal_gaps'] = gaps

    # Outliers (usando IQR)
    returns = df['close'].pct_change().dropna()
    Q1 = returns.quantile(0.25)
    Q3 = returns.quantile(0.75)
    IQR = Q3 - Q1
    outliers = ((returns < (Q1 - 1.5 * IQR)) | (returns > (Q3 + 1.5 * IQR))).sum()
    analysis['data_quality']['return_outliers'] = outliers

    return analysis, df

def print_analysis_report(analysis: dict):
    """Imprime relatório de análise formatado."""

    print(f"\n📊 RELATÓRIO DE QUALIDADE - {analysis['symbol']}")
    print("=" * 60)

    # Informações básicas
    print(f"📈 Total de registros: {analysis['total_records']:,}")
    print(f"📅 Período: {analysis['date_range']['start']} até {analysis['date_range']['end']}")
    print(f"⏱️  Duração: {analysis['date_range']['duration_days']} dias")

    # Qualidade dos dados
    print(f"\n🔍 QUALIDADE DOS DADOS:")
    quality = analysis['data_quality']
    print(f"   • Valores ausentes: {sum(quality['missing_values'].values())}")
    print(f"   • Timestamps duplicados: {quality['duplicate_timestamps']}")
    print(f"   • Velas com volume zero: {quality['zero_volume_candles']}")
    print(f"   • Preços negativos/zero: {quality['negative_prices']}")
    print(f"   • Inconsistências OHLC: {quality['ohlc_inconsistencies']}")
    print(f"   • Gaps temporais: {quality['temporal_gaps']}")
    print(f"   • Outliers de retorno: {quality['return_outliers']}")

    # Estatísticas de preço
    print(f"\n💰 ESTATÍSTICAS DE PREÇO:")
    price_stats = analysis['price_statistics']
    print(f"   • Preço mín/máx: ${price_stats['close_min']:.2f} / ${price_stats['close_max']:.2f}")
    print(f"   • Preço médio: ${price_stats['close_mean']:.2f}")
    print(f"   • Volatilidade diária: {price_stats['daily_volatility']:.2%}")
    print(f"   • Volatilidade anualizada: {price_stats['annualized_volatility']:.2%}")

    # Estatísticas de volume
    print(f"\n📊 ESTATÍSTICAS DE VOLUME:")
    vol_stats = analysis['volume_statistics']
    print(f"   • Volume mín/máx: {vol_stats['volume_min']:.0f} / {vol_stats['volume_max']:.0f}")
    print(f"   • Volume médio: {vol_stats['volume_mean']:.0f}")

def main():
    """Função principal de análise."""

    print("🌌 QUALIA - Análise de Qualidade dos Dados Históricos")
    print("=" * 60)

    # Caminhos dos arquivos
    data_dir = Path("data/historical")
    files = {
        'BTC_USDT': data_dir / "BTC_USDT_1h.csv",
        'ETH_USDT': data_dir / "ETH_USDT_1h.csv",
        'SOL_USDT': data_dir / "SOL_USDT_1h.csv"
    }

    all_analyses = {}
    all_dataframes = {}

    # Analisa cada arquivo
    for symbol, file_path in files.items():
        if file_path.exists():
            analysis, df = analyze_data_quality(str(file_path), symbol)
            all_analyses[symbol] = analysis
            all_dataframes[symbol] = df
            print_analysis_report(analysis)
        else:
            print(f"❌ Arquivo não encontrado: {file_path}")

    # Resumo geral
    print(f"\n🎯 RESUMO GERAL")
    print("=" * 60)

    total_records = sum(a['total_records'] for a in all_analyses.values())

    # Calcula total de issues de forma mais robusta
    total_issues = 0
    for analysis in all_analyses.values():
        quality = analysis['data_quality']
        for key, value in quality.items():
            if key == 'missing_values' and isinstance(value, dict):
                total_issues += sum(value.values())
            elif isinstance(value, (int, float)):
                total_issues += value

    print(f"📊 Total de registros analisados: {total_records:,}")
    print(f"⚠️  Total de issues identificadas: {total_issues}")

    # Recomendações
    print(f"\n💡 RECOMENDAÇÕES PARA BACKTESTING:")
    print("   ✅ Dados adequados para análise comparativa")
    print("   ✅ Período suficiente para backtesting robusto")
    print("   ✅ Timeframe de 1h apropriado para estratégias QUALIA")

    if total_issues > 0:
        print("   ⚠️  Considerar limpeza de dados antes dos backtests")

    return all_analyses, all_dataframes

if __name__ == "__main__":
    analyses, dataframes = main()